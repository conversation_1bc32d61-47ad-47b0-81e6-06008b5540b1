import calculateReadTime from "./calcReadTime";

export const getCategories = async () => {
  return ["All", "Data Extraction", "Document Processing"];
};

export async function getData() {
  // Fetch data from an API or database
  const popularBlogs = [
    {
      id: 1,
      title: "What to look for in a Document Processing Software?",
      // author: "Amit Timalsina",
      date: "November 19, 2024",
      readTime: calculateReadTime(
        "What to look for in a Document Processing Software?".length,
        200,
        1
      ),
      href: "what-to-look-for-in-a-document-processing-software",
      image:
        "https://images.unsplash.com/photo-1719937206098-236a481a2b6d?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      category: "Document Processing",
    },
    {
      id: 1,
      title: "What to look for in a Document Processing Software?",
      // author: "Amit Timalsina",
      date: "November 19, 2024",
      readTime: calculateReadTime(
        "What to look for in a Document Processing Software?".length,
        200,
        1
      ),
      href: "what-to-look-for-in-a-document-processing-software",
      image:
        "https://images.unsplash.com/photo-1719937206098-236a481a2b6d?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      category: "Document Processing",
    },
    {
      id: 1,
      title: "What to look for in a Document Processing Software?",
      // author: "Amit Timalsina",
      date: "November 19, 2024",
      readTime: calculateReadTime(
        "What to look for in a Document Processing Software?".length,
        200,
        1
      ),
      image:
        "https://images.unsplash.com/photo-1719937206098-236a481a2b6d?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      category: "Data Extraction",
      href: "what-to-look-for-in-a-document-processing-software",
    },
    {
      id: 1,
      title: "What to look for in a Document Processing Software?",
      // author: "Amit Timalsina",
      date: "November 19, 2024",
      readTime: calculateReadTime(
        "What to look for in a Documentdewp[ledwopkewdfkiopfwekopfeopkfkweopweopfkweopfkdfwefweiopkfweokp-fkweoopkwefkopwefkiopfewkopefwopkefwopkfwekopeopfkwkp] Pfkeioeifeiojfewijo feijoweiojfwjiofwe ijorocessing Software?"
          .length,
        200,
        1
      ),
      href: "what-to-look-for-in-a-document-processing-software",
      image:
        "https://images.unsplash.com/photo-1719937206098-236a481a2b6d?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      category: "OCR",
    },
    // Add more blog data
  ];

  const recentBlogs = [
    {
      id: 2,
      title: "How to extract data from PDF?",
      // author: "Amit Timalsina",
      date: "August 13, 2024",
      href: "what-to-look-for-in-a-document-processing-software",
      readTime: calculateReadTime(
        "What to look for in a Document Processing Software?".length,
        200,
        1
      ),
      category: "OCR",

      image:
        "https://images.unsplash.com/photo-1719937206098-236a481a2b6d?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      id: 2,
      title: "How to extract data from PDF?",
      // author: "Amit Timalsina",
      date: "August 13, 2024",
      href: "what-to-look-for-in-a-document-processing-software",
      readTime: calculateReadTime(
        "What to look for in a Document Processing Software?".length,
        200,
        1
      ),
      category: "OCR",

      image:
        "https://images.unsplash.com/photo-1719937206098-236a481a2b6d?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      id: 2,
      title: "How to extract data from PDF?",
      // author: "Amit Timalsina",
      date: "August 13, 2024",
      href: "what-to-look-for-in-a-document-processing-software",
      readTime: calculateReadTime(
        "What to look for in a Document Processing Software?".length,
        200,
        1
      ),
      category: "OCR",

      image:
        "https://images.unsplash.com/photo-1719937206098-236a481a2b6d?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },

    // Add more blog data
  ];

  const categories = ["All", "Data Extraction", "Document Processing"];

  return {
    popularBlogs,
    recentBlogs,
    categories,
  };
}