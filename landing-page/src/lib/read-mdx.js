import fs from "fs";
import path from "path";
import matter from "gray-matter";
import { remark } from "remark";
import { visit } from "unist-util-visit";
import readingTime from "remark-reading-time";

// https://github.com/mdx-js/mdx/issues/810

const postsDirectory = path.join(process.cwd(), "src/content", "blog-posts");

export function getAllPosts() {
  const fileNames = fs.readdirSync(postsDirectory);
  const posts = fileNames.map(fileName => {
    const filePath = path.join(postsDirectory, fileName);
    const fileContents = fs.readFileSync(filePath, "utf8");
    const { data, content } = matter(fileContents);

    const readingTimeData = calculateReadingTime(content);

    return {
      ...data,
      slug: data.url,
      readingTime: readingTimeData.text, // Add reading time to each post
    }; // Use `url` as slug
  });

  // Sort posts by date in descending order
  posts.sort((a, b) => new Date(b.date) - new Date(a.date));

  return posts;
}

export function getPostBySlug(slug) {
  try {
    const filePath = path.join(postsDirectory, `${slug}.mdx`);
    const fileContents = fs.readFileSync(filePath, "utf8");
    const { data, content } = matter(fileContents);

    const { headings, readingTime } = extractHeadingsAndReadingTime(content);

    console.log(headings);

    return { data, content, headings, readingTime };
  } catch (error) {
    console.log(error);
    return null;
  }
}

function calculateReadingTime(markdown) {
  let readingTimeData = null;

  remark()
    .use(readingTime)
    .use(() => (tree, file) => {
      readingTimeData = file.data.readingTime;
    })
    .processSync(markdown); // Synchronous processing

  return readingTimeData;
}
function extractHeadingsAndReadingTime(markdown) {
  const headings = [];
  let readingTimeData = null;

  remark()
    .use(() => tree => {
      visit(tree, "heading", node => {
        const text = node.children
          .filter(child => child.type === "text" || child.type === "link") // Include link text
          .map(child => child.value)
          .join("")
          .trim();

        // Exclude empty headings or headings that are just numbers
        if (text && !/^\d+(\.\d+)?$/.test(text)) {
          const id = text
            .toLowerCase()
            .replace(/[^\w\s-]/g, "") // Remove non-alphanumeric characters except spaces and hyphens
            .replace(/\s+/g, "-"); // Replace spaces with hyphens

          headings.push({ text, level: node.depth, id });
        }
      });
    })
    .use(readingTime)
    .use(() => (tree, file) => {
      readingTimeData = file.data.readingTime;
    })
    .processSync(markdown); // Synchronous processing

  return { headings, readingTime: readingTimeData };
}

function updateRelatedArticles() {
  const posts = getAllPosts();
  posts.forEach(post => {
    const relatedArticles = posts
      .filter(
        otherPost =>
          post.slug !== otherPost.slug && // Ensure the current post is not included
          post?.hashtags?.some(tag => otherPost?.hashtags?.includes(tag))
      )
      .map(otherPost => otherPost.slug); // Use slug for consistency

    // Update the current post file with its related articles
    const filePath = path.join(postsDirectory, post.slug + ".mdx");

    // ... existing code ...
    let fileContents;
    try {
      fileContents = fs.readFileSync(filePath, "utf8");
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      return; // Skip this post if there's an error reading the file
    }

    let data;
    try {
      data = matter(fileContents).data;
    } catch (error) {
      console.error(`Error parsing YAML in file ${filePath}:`, error);
      return; // Skip this post if there's an error parsing the YAML
    }

    data.relatedArticles = relatedArticles;

    try {
      fs.writeFileSync(filePath, matter.stringify(fileContents, data));
    } catch (error) {
      console.error(`Error writing file ${filePath}:`, error);
    }
  });
}

// updateRelatedArticles();
