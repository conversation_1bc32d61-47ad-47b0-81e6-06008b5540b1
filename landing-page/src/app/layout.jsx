import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  weights: [400, 500, 600, 700],
});
export const metadata = {
  title: "SpeedUpHire - AI Powered Smarter Job Search and Resume Optimization",
  description: "SpeedUpHire transforms the way recruiters find and hire top talent. With AI-driven candidate matching, automated outreach, and intelligent shortlisting, streamline your hiring process and connect with the best candidates—faster and smarter",
};

export default function RootLayout({ children }) {
  return (
    <html lang='en'>
      <body className={`${inter.className} relative`}>{children}</body>
    </html>
  );
}
