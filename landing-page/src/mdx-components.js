import React from "react";

// Utility function to generate headings with dynamic IDs
function generateHeading(tag, props) {
  const { children } = props;
  const id =
    typeof children === "string"
      ? children
          .toString()
          .toLowerCase()
          .replace(/\s+/g, "-") // Replace spaces with dashes
          .replace(/[()]/g, "") // Remove parentheses
          .replace(/\?/g, "") // Remove question marks
      : undefined;

  const className = `text-${
    tag === "h1"
      ? "3xl md:text-4xl"
      : tag === "h2"
      ? "2xl md:text-3xl"
      : tag === "h3"
      ? "xl md:text-2xl"
      : tag === "h4"
      ? "lg md:text-xl"
      : tag === "h5"
      ? "base md:text-lg"
      : "sm md:text-base"
  } font-bold mt-${
    tag === "h1"
      ? "6 md:mt-8"
      : tag === "h2"
      ? "5 md:mt-6"
      : tag === "h3"
      ? "4"
      : "3"
  } mb-${tag === "h1" ? "3 md:mb-4" : tag === "h2" ? "2 md:mb-3" : "2"}`;

  return React.createElement(tag, { id, className, ...props }, children);
}
export const MDXComponents = {
  h1: props => generateHeading("h1", props),
  h2: props => generateHeading("h2", props),
  h3: props => generateHeading("h3", props),
  p: props => <p className='text-base leading-7 my-4' {...props} />,
  ul: props => (
    <ul
      className='list-disc !pl-6 my-4'
      {...props}
      style={{ paddingLeft: "28px" }}
    />
  ),
  ol: props => (
    <ol
      className='list-decimal !pl-6 my-4'
      {...props}
      style={{ paddingLeft: "28px" }}
    />
  ),
  li: props => <li className='mb-2' {...props} />,
  a: props => (
    <a
      className='text-blue-600 hover:underline'
      target='_blank'
      rel='noopener'
      {...props}
    />
  ),
  blockquote: props => (
    <blockquote
      className='border-l-4 border-gray-400 pl-4 italic my-4'
      {...props}
    />
  ),
  code: props => (
    <code className='bg-gray-100 p-1 rounded text-red-500' {...props} />
  ),
  pre: props => (
    <pre className='bg-gray-100 p-4 rounded-lg overflow-x-auto' {...props} />
  ),
  table: props => (
    <table
      className='min-w-full bg-white border border-gray-200 my-4'
      {...props}
    />
  ),
  th: props => (
    <th
      className='px-4 py-2 border-b-2 border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-600'
      {...props}
    />
  ),
  td: props => (
    <td
      className='px-4 py-2 border-b border-gray-200 text-sm text-gray-700'
      {...props}
    />
  ),
};

export function useMDXComponents(components) {
  return {
    h1: props => (
      <h1
        className='text-3xl md:text-4xl font-bold mt-6 md:mt-8 mb-3 md:mb-4'
        {...props}
      />
    ),
    h2: props => (
      <h2
        className='text-2xl md:text-3xl font-bold mt-5 md:mt-6 mb-2 md:mb-3'
        {...props}
      />
    ),
    h3: props => (
      <h3 className='text-xl md:text-2xl font-bold   mt-4 mb-2' {...props} />
    ),
    h4: props => (
      <h4 className='text-lg md:text-xl font-bold mt-3 mb-2' {...props} />
    ),
    h5: props => (
      <h5 className='text-base md:text-lg font-bold mt-3 mb-2' {...props} />
    ),
    h6: props => (
      <h6 className='text-sm md:text-base font-bold mt-2 mb-1' {...props} />
    ),
    p: props => (
      <p
        className='text-base md:text-lg leading-7 md:leading-8 my-4'
        {...props}
      />
    ),
    ul: props => <ul className='list-disc pl-8 my-4' {...props} />,
    ol: props => <ol className='list-decimal pl-8 my-4' {...props} />,
    li: props => (
      <li className='text-base md:text-lg leading-7 md:leading-8' {...props} />
    ),
    a: props => <a className='text-blue-500 hover:underline' {...props} />,
    blockquote: props => (
      <blockquote className='border-l-4 border-blue-500 pl-4 my-4' {...props} />
    ),
    code: props => <code className='bg-gray-200 p-1 rounded' {...props} />,
    pre: props => <pre className='bg-gray-200 p-4 rounded' {...props} />,
    img: props => (
      <img className='rounded-lg w-full h-auto' sizes='100vw' {...props} />
    ),

    ...["h1", "h2", "h3", "h4"].reduce(
      (acc, heading) => ({
        ...acc,
        [heading]: props => {
          const { children } = props;
          const id = children
            .toString()
            .toLowerCase()
            .replace(/\s+/g, "-")
            .replace(/[()]/g, "")
            .replace(/\?/g, "");
          const className = `text-${
            heading === "h1"
              ? "3xl md:text-4xl"
              : heading === "h2"
              ? "2xl md:text-3xl"
              : heading === "h3"
              ? "xl md:text-2xl"
              : "lg md:text-xl"
          } font-bold mt-${
            heading === "h1"
              ? "6 md:mt-8"
              : heading === "h2"
              ? "5 md:mt-6"
              : heading === "h3"
              ? "4"
              : "3"
          } mb-${
            heading === "h1"
              ? "3 md:mb-4"
              : heading === "h2"
              ? "2 md:mb-3"
              : "2"
          }`;
          return React.createElement(
            heading,
            { id: `${id}`, className, ...props },
            children
          );
        },
      }),
      {}
    ),
    ...components,
  };
}
