"use client";
import { CardStack } from "./ui/card-stack";
import { cn } from "@/lib/utils";
export function Testimonials() {
  return (
    <section id='#testimonials' className="pb-8 pt-4">
      <p className='text-2xl font-semibold max-w-7xl mx-auto my-6 text-center'>
        Job Seekers Share Their Experience
      </p>
      <div className="h-[20rem] flex items-center justify-center w-full">
        <CardStack items={testimonials} />
      </div>
    </section>
  );
}

// Small utility to highlight the content of specific section of a testimonial content
export const Highlight = ({
  children,
  className
}) => {
  return (
    (<span
      className={cn(
        "font-bold bg-emerald-100 text-emerald-700 dark:bg-emerald-700/[0.2] dark:text-emerald-500 px-1 py-0.5",
        className
      )}>
      {children}
    </span>)
  );
};

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    designation: "Pre-Final Year Student",
    review: "SpeedUpHire interface is straightforward and simple. Checked my ATS score also really like the Score breakdown sections, also got great insights from the improvement subsection.",
  },

  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    designation: "Frontend Developer Intern",
    review: "This ATS resume tool 🔥 is brilliant .",
  },
  {
    id: 3,
    name: "Debolina Patra",
    designation: "Human Resource Manager",
    review: "I gave SpeedUpHire a try and found it really helpful! It was insightful to see how my resume scored and where I could improve for better ATS optimization. Appreciate you making this available for free.",
  },
  {
    id: 4,
    name: "Sweta Kumari",
    designation: "Frontend Developer",
    review: "I checked my resume's ATS score on SpeedupHire — it's a really cool and user-friendly tool! I appreciate you making all the features available for free. You've done a great job building something genuinely helpful for job seekers.",
  },
  {
    id: 5,
    name: "Rushikesh Falak",
    designation: "Software Developer",
    review: "Whoa my ats score is 85.75% and i loved the improvement suggestion it gave",
  },
  {
    id: 6,
    name: "Ganesh Kolase",
    designation: "Software Consultant",
    review: "Thank you so much Suraj for building SpeedupHire. It's awesome",
  },
  
];