"use client";
import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import Link from "next/link";
// import Image from "next/image";

export const FloatingNav = ({ navItems, className }) => {
  // const scrollToTop = () => {
  //   window.scrollTo({ top: 0, left: 0, behavior: "smooth" });
  // };
  return (
    <AnimatePresence mode='wait'>
      <motion.div
        className={cn(
          "flex max-w-fit  fixed top-4 inset-x-0 mx-auto border border-white/[0.2] rounded-full bg-bgColor shadow-[0px_2px_3px_-1px_rgba(0,0,0,0.1),0px_1px_0px_0px_rgba(25,28,33,0.02),0px_0px_0px_1px_rgba(25,28,33,0.08)] z-[5000] pr-2 pl-8 py-2  items-center justify-center space-x-4  px-4 bg-white",
          // backdrop-blur-sm backdrop-filter
          className
        )}
      >
        {navItems.map((navItem, idx) => (
          <Link
            key={`link=${idx}`}
            href={navItem.link}
            className={cn(
              "relative items-center flex space-x-1 text-black  hover:text-neutral-500 align-middle"
            )}
            // onClick={navItem.link === "/" ? scrollToTop : null}
          >
            {/* {navItem.link === "/" && (
              <div className='w-10 h-10 flex items-center justify-center '>
                <Image
                  priority
                  src={"/logo-without-caption.png"}
                  alt={`${navItem.name} logo`}
                  // fill={true}
                  width={40}
                  height={40}
                  className='block text-md'
                />
              </div>
            )} */}
            {/* <span className='block sm:hidden'>{navItem.icon}</span> */}
            <span
              className={`sm:block text-sm ${
                navItem.link === "/" ? "text-base font-bold mr-4" : ""
              }`}
            >
              {navItem.name}
            </span>
          </Link>
        ))}
        <a
          href='https://ats.speeduphire.com/auth/login'
          className='border text-sm font-medium relative border-neutral-200  text-black  px-5 py-2 rounded-full'
        >
          <span>Start Job Search</span>
          <span className='absolute inset-x-0 w-1/2 mx-auto -bottom-px bg-gradient-to-r from-transparent via-primaryColor to-transparent  h-px' />
        </a>
      </motion.div>
    </AnimatePresence>
  );
};
