"use client";

import { useState } from "react";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    organization: "",
    designation: "",
    message: "",
    hearAbout: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // Create FormData object for QueryCRM submission
      const submitData = new FormData();

      // Add hidden fields as required by QueryCRM
      submitData.append('form_type', 'college_workshop');
      submitData.append('source', 'SpeedUpHire.com');

      // Add form fields with QueryCRM expected names
      submitData.append('name', formData.name);
      submitData.append('email', formData.email);
      submitData.append('organization', formData.organization);
      submitData.append('designation', formData.designation);
      submitData.append('hear_about', formData.hearAbout);
      submitData.append('message', formData.message || 'No additional details provided');

      // Submit to QueryCRM
      const response = await fetch('https://querycrm.com/f/q3tai2y4og', {
        method: 'POST',
        body: submitData
      });

      if (response.ok) {
        setSubmitStatus("success");
        setFormData({
          name: "",
          email: "",
          organization: "",
          designation: "",
          message: "",
          hearAbout: ""
        });
      } else {
        throw new Error('Form submission failed');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus("error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      id='contact'
      className='flex flex-col my-20 justify-center items-center px-4'
    >
      <div className='max-w-2xl w-full'>
        <h2 className='mb-4 text-xl text-center sm:text-2xl font-semibold text-black'>
          We provide free workshops for colleges on ATS-friendly resume building, and AI-powered interview preparation.
        </h2>
        <p className='mb-8 text-center text-gray-600'>
          Contact us to schedule one.
        </p>

        <form onSubmit={handleSubmit} className='space-y-4 bg-white p-6 rounded-lg shadow-lg'>
          {/* Name Field */}
          <div>
            <label htmlFor="name" className='block text-sm font-medium text-gray-700 mb-2'>
              Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all'
              placeholder="Enter your full name"
            />
          </div>

          {/* Email Field */}
          <div>
            <label htmlFor="email" className='block text-sm font-medium text-gray-700 mb-2'>
              Email *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all'
              placeholder="Enter your email address"
            />
          </div>

          {/* Organization/College Name Field */}
          <div>
            <label htmlFor="organization" className='block text-sm font-medium text-gray-700 mb-2'>
              Organization / College Name *
            </label>
            <input
              type="text"
              id="organization"
              name="organization"
              value={formData.organization}
              onChange={handleChange}
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all'
              placeholder="Enter your organization or college name"
            />
          </div>

          {/* Designation/Role Field */}
          <div>
            <label htmlFor="designation" className='block text-sm font-medium text-gray-700 mb-2'>
              Designation *
            </label>
            <select
              id="designation"
              name="designation"
              value={formData.designation}
              onChange={handleChange}
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all'
            >
              <option value="">Select your designation</option>
              <option value="Professor">Professor</option>
              <option value="Assistant Professor">Assistant Professor</option>
              <option value="Associate Professor">Associate Professor</option>
              <option value="Dean">Dean</option>
              <option value="Head of Department">Head of Department</option>
              <option value="Career Counselor">Career Counselor</option>
              <option value="Placement Officer">Placement Officer</option>
              <option value="Training & Placement Head">Training & Placement Head</option>
              <option value="Academic Coordinator">Academic Coordinator</option>
              <option value="Student Affairs Officer">Student Affairs Officer</option>
              <option value="Principal">Principal</option>
              <option value="Vice Principal">Vice Principal</option>
              <option value="Director">Director</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Message/Details Field */}
          <div>
            <label htmlFor="message" className='block text-sm font-medium text-gray-700 mb-2'>
              Message *
            </label>
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              rows={3}
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all resize-vertical'
              placeholder="Tell us about your requirements, number of students, preferred dates, or any specific topics you'd like us to cover..."
            />
          </div>

          {/* How did you hear about us Field */}
          <div>
            <label htmlFor="hearAbout" className='block text-sm font-medium text-gray-700 mb-2'>
              How did you hear about us? *
            </label>
            <select
              id="hearAbout"
              name="hearAbout"
              value={formData.hearAbout}
              onChange={handleChange}
              required
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all'
            >
              <option value="">Select an option</option>
              <option value="Google Search">Google Search</option>
              <option value="LinkedIn">LinkedIn</option>
              <option value="Facebook">Facebook</option>
              <option value="Instagram">Instagram</option>
              <option value="Twitter/X">Twitter/X</option>
              <option value="Colleague Referral">Colleague Referral</option>
              <option value="Student Referral">Student Referral</option>
              <option value="Conference/Event">Conference/Event</option>
              <option value="Educational Forum">Educational Forum</option>
              <option value="Email Marketing">Email Marketing</option>
              <option value="Word of Mouth">Word of Mouth</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={isSubmitting}
              className='w-full bg-primaryColor disabled:bg-primaryColor text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:scale-100'
            >
              {isSubmitting ? "Scheduling..." : "Schedule Workshop"}
            </button>
          </div>

          {/* Status Messages */}
          {submitStatus === "success" && (
            <div className='p-4 text-primaryColor'>
              Thank you for your interest! We'll get back to you within 24 hours.
            </div>
          )}

          {submitStatus === "error" && (
            <div className='p-4 text-red-700'>
              Something went wrong. Please try again or contact us directly.
            </div>
          )}
        </form>
      </div>
    </div>
  );
}
