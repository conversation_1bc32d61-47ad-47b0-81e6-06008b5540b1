import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, AlertCircle, XCircle, Clock } from "lucide-react";

export default function ResumeReviewDisplay({ reviewResponse, showSignupPrompt = false, onSignupPrompt = () => {} }) {
  const { data = {} } = reviewResponse;

  // Helper function to get status color and icon
  const getStatusInfo = (status, score) => {
    if (typeof status === 'string') {
      switch (status.toLowerCase()) {
        case 'excellent':
          return { color: 'bg-green-100 text-green-800', icon: CheckCircle, textColor: 'text-green-600' };
        case 'good':
          return { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, textColor: 'text-blue-600' };
        case 'needs_improvement':
          return { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle, textColor: 'text-yellow-600' };
        case 'poor':
          return { color: 'bg-red-100 text-red-800', icon: XCircle, textColor: 'text-red-600' };
        default:
          return { color: 'bg-gray-100 text-gray-800', icon: Clock, textColor: 'text-gray-600' };
      }
    }

    // Fallback based on score if status is not provided
    if (score >= 90) return { color: 'bg-green-100 text-green-800', icon: CheckCircle, textColor: 'text-green-600' };
    if (score >= 75) return { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, textColor: 'text-blue-600' };
    if (score >= 60) return { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle, textColor: 'text-yellow-600' };
    return { color: 'bg-red-100 text-red-800', icon: XCircle, textColor: 'text-red-600' };
  };

  // List of categories to display, ordered for presentation
  const categories = [
    { key: "formatting_and_layout", label: "Formatting & Layout" },
    { key: "header_and_contact_info", label: "Header & Contact Info" },
    { key: "skills_summary", label: "Skills Summary" },
    { key: "work_experience", label: "Work Experience" },
    { key: "education", label: "Education" },
    { key: "certifications", label: "Certifications" },
    { key: "projects", label: "Projects" },
    { key: "achievements", label: "Achievements" },
    { key: "language_grammar_tone", label: "Language, Grammar & Tone" },
    { key: "ats_compatibility", label: "ATS Compatibility" }
  ];

  const renderSectionReview = (category) => {
    const sectionData = data[category.key];
    if (!sectionData) return null;

    // Handle both old string format and new object format
    if (typeof sectionData === 'string') {
      return (
        <Card key={category.key}>
          <CardHeader>
            <CardTitle className="text-lg">{category.label}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">{sectionData}</p>
          </CardContent>
        </Card>
      );
    }

    if (typeof sectionData === 'object' && sectionData.score !== undefined) {
      const statusInfo = getStatusInfo(sectionData.status, sectionData.score);
      const StatusIcon = statusInfo.icon;

      return (
        <Card key={category.key}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-lg">
              <span>{category.label}</span>
              <div className="flex items-center space-x-2">
                <StatusIcon className={`w-5 h-5 ${statusInfo.textColor}`} />
                <span className={`font-bold ${statusInfo.textColor}`}>
                  {sectionData.score}/100
                </span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Progress value={sectionData.score} className="w-full" />
            
            {sectionData.status && (
              <Badge className={statusInfo.color}>
                {sectionData.status.replace('_', ' ')}
              </Badge>
            )}

            {sectionData.feedback && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Feedback</h4>
                <p className="text-gray-600 text-sm">{sectionData.feedback}</p>
              </div>
            )}

            {sectionData.improvements && sectionData.improvements.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Improvements</h4>
                <ul className="space-y-1">
                  {sectionData.improvements.slice(0, 3).map((improvement, index) => (
                    <li key={index} className="flex items-start text-sm text-gray-600">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {improvement}
                    </li>
                  ))}
                  {sectionData.improvements.length > 3 && (
                    <li className="text-sm text-gray-500 italic">
                      +{sectionData.improvements.length - 3} more improvements available in full version
                    </li>
                  )}
                </ul>
              </div>
            )}

            {sectionData.examples && sectionData.examples.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Examples</h4>
                <ul className="space-y-1">
                  {sectionData.examples.slice(0, 2).map((example, index) => (
                    <li key={index} className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                      {example}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      );
    }

    return null;
  };

  return (
    <div className="space-y-6">
      {/* Header with Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>{data.title}</span>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-3xl font-bold text-blue-600">{data.overall_score}/100</div>
                <div className="text-sm text-gray-500">Overall Score</div>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {data.experience_level && (
              <div>
                <span className="text-sm font-medium text-gray-500">Experience Level</span>
                <Badge variant="outline" className="ml-2 capitalize">
                  {data.experience_level.replace('_', ' ')}
                </Badge>
              </div>
            )}
            {data.resume_type && (
              <div>
                <span className="text-sm font-medium text-gray-500">Resume Type</span>
                <Badge variant="outline" className="ml-2 capitalize">
                  {data.resume_type}
                </Badge>
              </div>
            )}
          </div>
          <Progress value={data.overall_score} className="w-full" />
        </CardContent>
      </Card>

      {/* Top Priorities */}
      {data.top_priorities && data.top_priorities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2 text-red-500" />
              Top Priorities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {data.top_priorities.slice(0, 3).map((priority, index) => (
                <li key={index} className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{priority}</span>
                </li>
              ))}
              {data.top_priorities.length > 3 && (
                <li className="text-sm text-gray-500 italic ml-9">
                  +{data.top_priorities.length - 3} more priorities in full analysis
                </li>
              )}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Section-wise Reviews - Show only first 3 sections for preview */}
      <div className="grid gap-6">
        {categories.slice(0, 3).map((category) => renderSectionReview(category))}
      </div>

      {/* Signup Prompt */}
      {showSignupPrompt && (
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-blue-900 mb-2">
                  🎉 Great! Your resume has been analyzed
                </h4>
                <p className="text-blue-700 text-sm mb-4">
                  This is just a preview! Sign up for free to unlock:
                </p>
                <ul className="text-blue-700 text-sm space-y-1 mb-4">
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                    Complete analysis of all {categories.length} sections
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                    ATS score checker with job description matching
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                    AI-powered resume tailoring for specific jobs
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                    Save and track your resume improvements
                  </li>
                </ul>
                <div className="flex flex-col sm:flex-row gap-3">
                  <a
                    href="https://ats.speeduphire.com/auth/login"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center bg-blue-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:bg-blue-700 transition-colors"
                  >
                    Get Full Analysis - Free
                  </a>
                  <button
                    onClick={() => onSignupPrompt(false)}
                    className="inline-flex items-center justify-center border border-blue-300 text-blue-700 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-blue-50 transition-colors"
                  >
                    Maybe Later
                  </button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
