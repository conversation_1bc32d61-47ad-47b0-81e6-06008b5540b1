"use client";
import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";

const ProductShowcase = () => {
  return (
    <section className="py-16 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">
            AI-Powered Resume Optimization
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Transform your resume with our advanced resume scoring system and intelligent tailoring features
          </p>
        </div>

        <div className="space-y-16">
          {/* ATS Score Feature */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div>
                <h3 className="text-2xl font-semibold mb-4">
                  Advanced Resume Score Analysis
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Get detailed insights into how your resume performs against Applicant Tracking Systems.
                  Our AI analyzes your resume and provides actionable feedback to improve your chances of
                  getting past the initial screening.
                </p>
                <ul className="mt-4 space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Comprehensive scoring breakdown
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Section-wise analysis
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Detailed improvement suggestions
                  </li>
                </ul>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative rounded-lg overflow-hidden shadow-2xl">
                <Image
                  src="/images/ats_score.png"
                  alt="ATS Score Analysis Dashboard"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover"
                />
              </div>
            </motion.div>
          </div>

          {/* ATS Score Improvement Feature */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="relative lg:order-2"
            >
              <div className="relative rounded-lg overflow-hidden shadow-2xl">
                <Image
                  src="/images/banner.png"
                  alt="ATS Score Improvement Analysis - Before and After Comparison"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover"
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-6 lg:order-1"
            >
              <div>
                <h3 className="text-2xl font-semibold mb-4">
                  Resume Tailoring with Proven Results
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  See the dramatic improvement in your resume scores with our intelligent resume tailoring.
                  Our AI optimizes your resume for specific job descriptions, showing you exactly what
                  changed and how much your score improved.
                </p>
                <ul className="mt-4 space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="w-2 h-2  bg-green-500 rounded-full mr-3"></span>
                    Before/after score comparison
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2  bg-green-500 rounded-full mr-3"></span>
                    Detailed improvement breakdown
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2  bg-green-500 rounded-full mr-3"></span>
                    Job-specific optimization
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2  bg-green-500 rounded-full mr-3"></span>
                    Proven score improvements (+42 points average)
                  </li>
                </ul>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <a
            href="https://ats.speeduphire.com/auth/login"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block bg-primaryColor hover:bg-primaryColor/90 text-white px-8 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
          >
            Try It Free Now
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default ProductShowcase;
