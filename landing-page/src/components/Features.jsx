import { cn } from "@/lib/utils";
import React from "react";
import { BentoGrid, BentoGridItem } from "./ui/bento-grid";

import Image from "next/image";
const Features = () => {
  return (
    <section className='pb-8 pt-4'>
      <p className='text-2xl font-semibold max-w-7xl mx-auto  my-6'>
        {/* Spend time innovating, with the tools you need to move your business */}
        Features
      </p>
      <div className='flex flex-col lg:flex-row-reverse max-w-7xl mx-auto items-center gap-10 h-fit'>
        {/* <img
          alt='test'
          src={"/test.png"}
          className='lg:w-1/2 shadow-xl h-[30rem]'
        /> */}
        <BentoGrid className='max-w-7xl mx-auto md:auto-rows-auto'>
          {items.map((item, i) => (
            <BentoGridItem
              key={i}
              title={item.title}
              description={item.description}
              header={item.header}
              className={item.className}
              icon={item.icon}
            />
          ))}
        </BentoGrid>
      </div>
    </section>
  );
};
const Skeleton = ({ img, isCover = false }) => (
  <div className='flex flex-1 w-full h-full min-h-[6rem] rounded-xl '>
    <Image
      src={img}
      alt='img'
      // width={600}
      height={260}
      className={`object-contain bg-center w-full h-[18rem] rounded-xl shadow-sm`}
      style={{ objectFit: isCover ? "cover" : "contain" }}
    />
  </div>
);
const items = [
  {
    title: "Find Jobs That Match Your Resume",
    description:
      "Upload your resume, and let AI search across multiple job boards to find the best job opportunities tailored to your skills and experience.",
    className: "md:col-span-1",
  },
  {
    title: "Optimize Your Resume for ATS",
    description:
      "Run your resume through our ATS checker to ensure it gets past applicant tracking systems and lands in front of recruiters.",
    className: "md:col-span-1",
  },

  {
    title: "Generate a Tailored Cover Letter Instantly",
    description:
      "Let AI create a professional, job-specific cover letter that highlights your strengths and matches the job description.",
    className: "md:col-span-1",
  },
  {
    title: "Build a Professional Resume in Minutes",
    description:
      "Use our AI-powered resume builder to create a polished, well-structured resume that stands out to employers.",
    className: "md:col-span-1",
  },
];
export default Features;
