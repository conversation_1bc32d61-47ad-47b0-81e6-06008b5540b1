"use client";
import React from "react";
import Image from "next/image";
import { HeroHighlight, Highlight } from "./ui/hero-highlight";
import { motion } from "framer-motion";
const HeroSection = () => {
  return (
    <section
      id='#about'
      className=' flex flex-col items-center justify-center  pb-8 pt-4 overflow-hidden'
    >
      <div className='text-center'>
        <h1 className='text-3xl font-bold mb-4 leading-loose max-w-[60rem]'>
          From Resume to Job Offer - Job Search & Resume Optimization!
        </h1>
        <HeroHighlight>
          <motion.p
            initial={{
              opacity: 0,
              y: 20,
            }}
            animate={{
              opacity: 1,
              y: [20, -5, 0],
            }}
            transition={{
              duration: 0.5,
              ease: [0.4, 0.0, 0.2, 1],
            }}
            className='mb-10 max-w-[40rem] text-left mx-auto leading-relaxed   text-gray-500 flex flex-col gap-1'
          >
            SpeedUpHire finds the best jobs for your resume, optimizes it for ATS, generates tailored cover letters, and helps you build a professional resume. <br />
            <Highlight className='text-transparent block w-fit'>
              Smarter job search. Faster applications. Better results.
            </Highlight>
          </motion.p>
        </HeroHighlight>
        {/* <p className='text-gray-500 mb-10 max-w-[30rem] text-center mx-auto leading-relaxed'>
          Say goodbye to the hassle of searching for HS codes! Chat with our
          smart bot or upload a CSV to get codes in seconds. With intelligent
          suggestions and built-in verification, simplify customs and avoid
          costly mistakes. Focus on growing your business – we’ll handle the
          codes.
        </p> */}
        <a
          href='https://ats.speeduphire.com/auth/login'
          target='_blank'
          rel='noopener noreferrer'
          className='border-primaryColor border-2 hover:bg-primaryColor transition-all hover:text-white px-6 py-3 rounded-md font-semibold'
        >
          Free Resume Score Check
        </a>
      </div>
      {/* <ContainerScroll>
        <BackgroundBeamsWithCollision>
          <Image
            src={heroImage}
            alt='hero'
            // height={720}
            // width={1400}
            className='mx-auto rounded-2xl object-cover h-full object-left-top'
            draggable={false}
          />
        </BackgroundBeamsWithCollision>
      </ContainerScroll> */}
    </section>
  );
};

export default HeroSection;
