"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { Upload, FileText, CheckCircle, AlertCircle, XCircle, Clock } from "lucide-react";

const ResumeReviewSection = () => {
  const [resumeFile, setResumeFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [reviewResponse, setReviewResponse] = useState(null);
  const [validationError, setValidationError] = useState("");
  const [showSignupPrompt, setShowSignupPrompt] = useState(false);

  const handleResumeUpload = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        setValidationError("Please upload a PDF file only");
        return;
      }
      setResumeFile(file);
      setValidationError("");
    }
  };

  const handleSubmit = async () => {
    if (!resumeFile) {
      setValidationError("Please upload a resume file");
      return;
    }

    const formData = new FormData();
    formData.append("resume", resumeFile);

    try {
      setIsLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI || 'https://api.speeduphire.com'}/api/public/review-resume`,
        {
          method: "POST",
          body: formData,
        }
      );

      if (response.status === 429) {
        setValidationError("Rate limit exceeded. Please try again later.");
        return;
      }

      if (!response.ok) {
        throw new Error("Failed to review resume");
      }

      const data = await response.json();
      setReviewResponse(data);
      setShowSignupPrompt(true);
    } catch (error) {
      console.error(error);
      setValidationError("Error reviewing resume. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case "excellent":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "good":
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case "needs improvement":
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case "poor":
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const renderSectionScore = (sectionName, sectionData) => {
    if (typeof sectionData === "string") {
      return (
        <div key={sectionName} className="p-4 border rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2 capitalize">
            {sectionName.replace(/_/g, " ")}
          </h4>
          <p className="text-gray-600 text-sm">{sectionData}</p>
        </div>
      );
    }

    if (typeof sectionData === "object" && sectionData.score !== undefined) {
      return (
        <div key={sectionName} className="p-4 border rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900 capitalize">
              {sectionName.replace(/_/g, " ")}
            </h4>
            <div className="flex items-center space-x-2">
              {getStatusIcon(sectionData.status)}
              <span className={`font-semibold ${getScoreColor(sectionData.score)}`}>
                {sectionData.score}/100
              </span>
            </div>
          </div>
          
          {sectionData.feedback && (
            <p className="text-gray-600 text-sm mb-3">{sectionData.feedback}</p>
          )}
          
          {sectionData.improvements && sectionData.improvements.length > 0 && (
            <div className="space-y-1">
              <h5 className="text-sm font-medium text-gray-700">Improvements:</h5>
              <ul className="text-sm text-gray-600 space-y-1">
                {sectionData.improvements.slice(0, 2).map((improvement, index) => (
                  <li key={index} className="flex items-start">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                    {improvement}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  return (
    <section className="py-16 px-4 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold mb-4 text-gray-900">
            Try Our Free Resume Review
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Get instant feedback on your resume with our AI-powered analysis. 
            Discover what's working and what needs improvement to land your dream job.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Upload Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                Upload Your Resume
              </h3>
              
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                  <input
                    type="file"
                    accept=".pdf"
                    onChange={handleResumeUpload}
                    className="hidden"
                    id="resume-upload"
                  />
                  <label htmlFor="resume-upload" className="cursor-pointer">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">
                      Click to upload your resume
                    </p>
                    <p className="text-sm text-gray-500">PDF files only</p>
                  </label>
                </div>

                {resumeFile && (
                  <div className="flex items-center space-x-2 text-green-600">
                    <FileText className="w-4 h-4" />
                    <span className="text-sm">{resumeFile.name}</span>
                  </div>
                )}

                {validationError && (
                  <p className="text-red-600 text-sm">{validationError}</p>
                )}

                <button
                  onClick={handleSubmit}
                  disabled={isLoading || !resumeFile}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? "Analyzing..." : "Get Free Review"}
                </button>
              </div>
            </div>
          </motion.div>

          {/* Results Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {reviewResponse ? (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">
                      {reviewResponse.data.title}
                    </h3>
                    <div className="text-right">
                      <div className={`text-3xl font-bold ${getScoreColor(reviewResponse.data.overall_score)}`}>
                        {reviewResponse.data.overall_score}/100
                      </div>
                      <div className="text-sm text-gray-500">Overall Score</div>
                    </div>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div
                      className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${reviewResponse.data.overall_score}%` }}
                    ></div>
                  </div>
                </div>

                {/* Top Priorities */}
                {reviewResponse.data.top_priorities && reviewResponse.data.top_priorities.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-medium text-gray-900 mb-3">Top Priorities</h4>
                    <div className="space-y-2">
                      {reviewResponse.data.top_priorities.slice(0, 3).map((priority, index) => (
                        <div key={index} className="flex items-start">
                          <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          <span className="text-gray-600 text-sm">{priority}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Section Scores Preview */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Section Analysis</h4>
                  <div className="grid grid-cols-1 gap-3 max-h-64 overflow-y-auto">
                    {Object.entries(reviewResponse.data)
                      .filter(([key, value]) => 
                        key.includes('_') && 
                        !['top_priorities', 'quick_wins', 'long_term_goals', 'strengths', 'overall_summary', 'section_weights', 'weightage_reasoning'].includes(key) &&
                        (typeof value === 'string' || (typeof value === 'object' && value.score !== undefined))
                      )
                      .slice(0, 4)
                      .map(([key, value]) => renderSectionScore(key, value))
                    }
                  </div>
                </div>

                {/* Signup Prompt */}
                {showSignupPrompt && (
                  <div className="mt-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <CheckCircle className="w-6 h-6 text-blue-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-blue-900 mb-2">
                          🎉 Great! Your resume has been analyzed
                        </h4>
                        <p className="text-blue-700 text-sm mb-4">
                          This is just a preview! Sign up for free to unlock:
                        </p>
                        <ul className="text-blue-700 text-sm space-y-1 mb-4">
                          <li className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                            Complete detailed analysis of all sections
                          </li>
                          <li className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                            ATS score checker with job description matching
                          </li>
                          <li className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                            AI-powered resume tailoring for specific jobs
                          </li>
                          <li className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                            Save and track your resume improvements
                          </li>
                        </ul>
                        <div className="flex flex-col sm:flex-row gap-3">
                          <a
                            href="https://ats.speeduphire.com/auth/login"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center justify-center bg-blue-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:bg-blue-700 transition-colors"
                          >
                            Get Full Analysis - Free
                          </a>
                          <button
                            onClick={() => setShowSignupPrompt(false)}
                            className="inline-flex items-center justify-center border border-blue-300 text-blue-700 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-blue-50 transition-colors"
                          >
                            Maybe Later
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Ready to Analyze
                </h3>
                <p className="text-gray-600 mb-4">
                  Upload your resume to see a detailed analysis of your content,
                  formatting, and ATS compatibility.
                </p>
                <div className="text-sm text-gray-500 mb-4">
                  ✨ Free preview • No signup required
                </div>
                <p className="text-xs text-gray-400">
                  Want the full experience?
                  <a
                    href="https://ats.speeduphire.com/auth/login"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-700 ml-1"
                  >
                    Sign up for free
                  </a>
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ResumeReviewSection;
