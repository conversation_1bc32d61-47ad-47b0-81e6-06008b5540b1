"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { Upload, FileText } from "lucide-react";
import ResumeReviewDisplay from "./ResumeReviewDisplay";

const ResumeReviewSection = () => {
  const [resumeFile, setResumeFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [reviewResponse, setReviewResponse] = useState(null);
  const [validationError, setValidationError] = useState("");
  const [showSignupPrompt, setShowSignupPrompt] = useState(false);

  const handleResumeUpload = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        setValidationError("Please upload a PDF file only");
        return;
      }
      setResumeFile(file);
      setValidationError("");
    }
  };

  const handleSubmit = async () => {
    if (!resumeFile) {
      setValidationError("Please upload a resume file");
      return;
    }

    const formData = new FormData();
    formData.append("resume", resumeFile);

    try {
      setIsLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI || 'https://api.speeduphire.com'}/api/public/review-resume`,
        {
          method: "POST",
          body: formData,
        }
      );

      if (response.status === 429) {
        setValidationError("Rate limit exceeded. Please try again later.");
        return;
      }

      if (!response.ok) {
        throw new Error("Failed to review resume");
      }

      const data = await response.json();
      setReviewResponse(data);
      setShowSignupPrompt(true);
    } catch (error) {
      console.error(error);
      setValidationError("Error reviewing resume. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <section className="py-16 px-4 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold mb-4 text-gray-900">
            Try Our Free Resume Review
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Get instant feedback on your resume with our AI-powered analysis. 
            Discover what's working and what needs improvement to land your dream job.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Upload Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                Upload Your Resume
              </h3>
              
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                  <input
                    type="file"
                    accept=".pdf"
                    onChange={handleResumeUpload}
                    className="hidden"
                    id="resume-upload"
                  />
                  <label htmlFor="resume-upload" className="cursor-pointer">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">
                      Click to upload your resume
                    </p>
                    <p className="text-sm text-gray-500">PDF files only</p>
                  </label>
                </div>

                {resumeFile && (
                  <div className="flex items-center space-x-2 text-green-600">
                    <FileText className="w-4 h-4" />
                    <span className="text-sm">{resumeFile.name}</span>
                  </div>
                )}

                {validationError && (
                  <p className="text-red-600 text-sm">{validationError}</p>
                )}

                <button
                  onClick={handleSubmit}
                  disabled={isLoading || !resumeFile}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? "Analyzing..." : "Get Free Review"}
                </button>
              </div>
            </div>
          </motion.div>

          {/* Results Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {reviewResponse ? (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <ResumeReviewDisplay
                  reviewResponse={reviewResponse}
                  showSignupPrompt={showSignupPrompt}
                  onSignupPrompt={setShowSignupPrompt}
                />
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Ready to Analyze
                </h3>
                <p className="text-gray-600 mb-4">
                  Upload your resume to see a detailed analysis of your content,
                  formatting, and ATS compatibility.
                </p>
                <div className="text-sm text-gray-500 mb-4">
                  ✨ Free preview • No signup required
                </div>
                <p className="text-xs text-gray-400">
                  Want the full experience?
                  <a
                    href="https://ats.speeduphire.com/auth/login"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-700 ml-1"
                  >
                    Sign up for free
                  </a>
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ResumeReviewSection;
