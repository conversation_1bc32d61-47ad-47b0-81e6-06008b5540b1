import { IconBrandLinkedin } from "@tabler/icons-react";
import Link from "next/link";

const Footer = () => {
  return (
    <footer className='w-full relative py-8 bg-bgDark flex flex-col items-center justify-center'>
      <div className='flex  flex-col md:flex-row items-center justify-between w-full max-w-6xl px-4'>
        <div className='flex flex-col items-center md:items-start text-white'>
          {/* <Image
            src='/logo.svg'
            alt='DocExtend Logo'
            fill={true}
            className='h-8 mb-2'
          /> */}
          <h2 className='text-3xl mb-1 font-semibold'>SpeedUpHire</h2>
          <p className='text-sm text-slate-300 text-center md:text-left'>
            Accelerate Your Job Search with AI-Powered Resume Optimization.
          </p>
        </div>
        {/* <div className='flex flex-col items-center md:items-start mt-4 md:mt-0 text-slate-300 text-sm'>
          <Link href='/privacy-policy' className='hover:underline'>
            Privacy Policy
          </Link>
        </div>
        <div className='mt-4 md:mt-0'>
          <Link href='' target='_blank' rel='noopener noreferrer'>
            <IconBrandLinkedin className='text-white h-10 w-10' />
          </Link>
        </div> */}
      </div>

      {/* Copyright Text */}
      <div className='mt-6 text-center text-sm text-gray-500'>
        © 2025 Future Gen AI Services All rights reserved.
      </div>
      {/* Links */}
      <div className='flex justify-center space-x-4 text-sm text-gray-500'>
        <a href='/disclaimer' className='hover:text-gray-300'>
          Disclaimer
        </a>
        <a href='/faq' className='hover:text-gray-300'>
          FAQ
        </a>
        <a
          href='/privacy-policy'
          className='hover:text-gray-300'
        >
          Privacy Policy
        </a>
        <a
          href='/terms-of-service'
          className='hover:text-gray-300'
        >
          Terms of Service
        </a>
      </div>
    </footer>
  );
};

export default Footer;
