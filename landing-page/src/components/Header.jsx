// "use client";
// import React from "react";
// import { FloatingNav } from "./ui/floating-nav";
// import { IconMessage, IconUser } from "@tabler/icons-react";
// export function Header() {
//   // https://react-svgr.com/playground/ svg to react component
//   const navItems = [
//     {
//       name: "SpeedUpHire",
//       link: "/",
//       // icon: <IconHome2 className='h-4 w-4 text-neutral-500 ' />,
//     },
//     // {
//     //   name: "Pricing",
//     //   link: "#pricing",
//     //   icon: <IconCreditCard className='h-4 w-4 text-neutral-500 ' />,
//     // },
//     {
//       name: "About",
//       link: "#about",
//       icon: <IconUser className='h-4 w-4 text-neutral-500 dark:text-white' />,
//     },
//     {
//       name: "Contact",
//       link: "#contact",
//       icon: (
//         <IconMessage className='h-4 w-4 text-neutral-500 dark:text-white' />
//       ),
//     },
//     {
//       name: "Blog",
//       link: "/blog",
//       icon: (
//         <IconMessage className='h-4 w-4 text-neutral-500 dark:text-white' />
//       ),
//     },
//   ];
//   return (
//     <div className='relative  w-full'>
//       <FloatingNav navItems={navItems} />
//     </div>
//   );
// }
"use client";
import React from "react";
import { FloatingNav } from "./ui/floating-nav";
import { 
  IconMessage, 
  IconUser, 
  IconArticle, 
  IconHome2 
} from "@tabler/icons-react";

export function Header() {
  const navItems = [
    {
      name: "SpeedUpHire",
      link: "/",
      icon: <IconHome2 className='h-4 w-4 text-neutral-500 dark:text-white' />,
    },
    // {
    //   name: "Pricing",
    //   link: "#pricing",
    //   icon: <IconCreditCard className='h-4 w-4 text-neutral-500 dark:text-white' />,
    // },
    {
      name: "About",
      link: "#about",
      icon: <IconUser className='h-4 w-4 text-neutral-500 dark:text-white' />,
    },
    {
      name: "Contact",
      link: "#contact",
      icon: <IconMessage className='h-4 w-4 text-neutral-500 dark:text-white' />,
    },
    // {
    //   name: "Blog",
    //   link: "/blog",
    //   icon: <IconArticle className='h-4 w-4 text-neutral-500 dark:text-white' />,
    // },
  ];

  return (
    <div className='relative w-full'>
      <FloatingNav navItems={navItems} />
    </div>
  );
}