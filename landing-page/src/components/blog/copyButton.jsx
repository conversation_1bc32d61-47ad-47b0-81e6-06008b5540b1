"use client";
import React, { useEffect, useState } from "react";

const Button = ({ icon, copyLink }) => {
  const [linkToCopy, setLinkToCopy] = useState("");

  useEffect(() => {
    setLinkToCopy(copyLink || window.location.href);
  }, [copyLink]);

  return (
    <button
      className='cursor-pointer'
      onClick={() => {
        navigator.clipboard.writeText(linkToCopy);
      }}
    >
      {icon}
    </button>
  );
};

export default Button;
