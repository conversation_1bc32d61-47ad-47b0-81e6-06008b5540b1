"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

const Dropdown = ({ categories }) => {
  const [isTypeOpen, setIsTypeOpen] = useState(false);
  const pathname = usePathname();
  const lastPathSegment = pathname.split("/").pop();
  return (
    <div className='flex items-center relative'>
      <label
        onClick={() => setIsTypeOpen(!isTypeOpen)}
        className='mr-2 md:block hidden font-medium'
        htmlFor='field-type'
      >
        Category
      </label>

      <div className='flex items-center relative '>
        <input
          onClick={() => setIsTypeOpen(!isTypeOpen)}
          type='button'
          value={"Select Category"}
          className='border px-4 py-3 rounded-md text-[#636c72]  cursor-pointer pr-10 text-left   bg-transparent placeholder:text-slate-400  text-sm  border-slate-200 transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-400 shadow-sm focus:shadow-md appearance-none md:w-[300px] '
          placeholder='Select Category'
          //   value={field.type}
        />
        <span className='ml-2 absolute right-4'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            className='h-4 w-4 text-[#636c72]'
            fill='none'
            viewBox='0 0 24 24'
            stroke='currentColor'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M19 9l-7 7-7-7'
            />
          </svg>
        </span>

        {isTypeOpen && (
          <div
            className='absolute right-0 top-12 z-10  mr-8 translate-x-8 w-full origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none max-h-[400px]  overflow-auto'
            role='menu'
            aria-orientation='vertical'
            aria-labelledby='menu-button'
            tabindex='-1'
          >
            <ul className='p-1 pb-0 pt-0 flex flex-col w-full'>
              {categories.map(category => (
                <a
                  key={category}
                  href={`/blog/category/${category
                    .split(" ")
                    .join("-")
                    .toLowerCase()}`}
                  className={`p-3 font-medium border-b hover:bg-gray-50 cursor-pointer text-sm ${
                    lastPathSegment ===
                    `${category.split(" ").join("-").toLowerCase()}`
                      ? "text-primaryColor"
                      : ""
                  }`}
                >
                  {category}
                </a>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dropdown;
