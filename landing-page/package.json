{"name": "hscode-landing-page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@next/mdx": "^15.0.3", "@tabler/icons-react": "^3.22.0", "@types/mdx": "^2.0.13", "clsx": "^2.1.1", "framer-motion": "^11.11.15", "gray-matter": "^4.0.3", "lucide-react": "^0.525.0", "mini-svg-data-uri": "^1.4.4", "motion": "^12.5.0", "next": "^15.3.1", "next-mdx-remote": "^5.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-reading-time": "^2.0.1", "tailwind-merge": "^3.0.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1"}}