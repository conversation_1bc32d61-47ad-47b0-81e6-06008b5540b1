<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATS-Friendly Resume</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 font-sans">
    <div class="max-w-4xl mx-auto bg-white shadow-md p-8 mt-8">
        <!-- Header -->
        <div class="text-center mb-6">
            <h1 class="text-4xl font-bold text-gray-900"><PERSON></h1>
            <p class="text-lg text-gray-600">Full-Stack Developer</p>
        </div>

        <!-- Contact Information -->
        <div class="flex justify-center space-x-6 mb-6">
            <div>
                <p class="text-gray-700"><strong>Email:</strong> <EMAIL></p>
            </div>
            <div>
                <p class="text-gray-700"><strong>Phone:</strong> (*************</p>
            </div>
            <div>
                <p class="text-gray-700"><strong>Location:</strong> San Francisco, CA</p>
            </div>
        </div>

        <!-- Summary -->
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-2">Professional Summary</h2>
            <p class="text-gray-700">
                Experienced Full-Stack Developer with 5+ years of expertise in designing and building scalable web applications. Proficient in JavaScript, React.js, Node.js, and database management. Passionate about creating user-friendly interfaces and writing clean, efficient code.
            </p>
        </div>

        <!-- Skills -->
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-2">Key Skills</h2>
            <ul class="list-disc list-inside text-gray-700">
                <li>JavaScript (ES6+), React.js, Node.js</li>
                <li>RESTful API Development</li>
                <li>Database: PostgreSQL, MongoDB</li>
                <li>Version Control: Git, GitHub</li>
                <li>Testing: Jest, Mocha</li>
            </ul>
        </div>

        <!-- Work Experience -->
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-2">Work Experience</h2>

            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Senior Full-Stack Developer</h3>
                <p class="text-gray-600">ABC Tech Solutions, San Francisco, CA</p>
                <p class="text-gray-500">June 2019 - Present</p>
                <ul class="list-disc list-inside text-gray-700">
                    <li>Designed and implemented scalable web applications using React.js and Node.js.</li>
                    <li>Led a team of 5 developers to deliver features on time.</li>
                    <li>Collaborated with the UI/UX team to create intuitive and responsive user interfaces.</li>
                    <li>Optimized application performance by 20% through refactoring code and updating database queries.</li>
                </ul>
            </div>

            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Junior Developer</h3>
                <p class="text-gray-600">XYZ Innovations, New York, NY</p>
                <p class="text-gray-500">Jan 2017 - May 2019</p>
                <ul class="list-disc list-inside text-gray-700">
                    <li>Developed REST APIs for the backend using Node.js and Express.</li>
                    <li>Integrated third-party services like Stripe and SendGrid for payments and email notifications.</li>
                    <li>Wrote unit and integration tests for critical application features.</li>
                </ul>
            </div>
        </div>

        <!-- Education -->
        <div>
            <h2 class="text-xl font-bold text-gray-800 mb-2">Education</h2>
            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Bachelor of Science in Computer Science</h3>
                <p class="text-gray-600">University of California, Berkeley</p>
                <p class="text-gray-500">Graduated: 2016</p>
            </div>
        </div>
    </div>
</body>
</html>
