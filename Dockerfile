# Use Python 3.9 as the base image
FROM python:3.9

WORKDIR /app

# Install system dependencies required for Playwright
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    xvfb \
    libnss3 \
    libxss1 \
    libasound2 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libgdk-pixbuf2.0-0 \
    libgtk-3-0 \
    libx11-xcb1 \
    libxcb-dri3-0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libwayland-client0 \
    libwayland-egl1 \
    libwayland-server0 \
    libxkbcommon0 \
    libfreetype6 \
    && rm -rf /var/lib/apt/lists/*

# Install Playwright and required dependencies
RUN pip install --no-cache-dir playwright

# Install Chromium browser for Playwright
RUN playwright install --with-deps chromium

# Copy the dependencies file
COPY ats-backend/requirements.txt /app/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r ./requirements.txt

# Copy the application code
COPY ats-backend/app /app

# Expose the port the app runs on
EXPOSE 8000

# Start Uvicorn
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
