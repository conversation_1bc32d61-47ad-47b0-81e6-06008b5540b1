<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATS-Compatible Modern Resume</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 font-sans">
    <div class="max-w-3xl mx-auto bg-white shadow-md mt-10 p-6 rounded-lg">
        <!-- Header Section -->
        <div class="text-center border-b pb-4 mb-6">
            <h1 class="text-5xl font-bold text-gray-900">John <PERSON></h1>
            <p class="text-xl text-gray-500">Software Engineer</p>
            <div class="flex justify-center mt-4 space-x-8 text-gray-700">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> (*************</p>
                <p><strong>Location:</strong> Seattle, WA</p>
            </div>
        </div>

        <!-- Professional Summary -->
        <div class="bg-indigo-100 p-6 mb-6 rounded-lg shadow-sm">
            <h2 class="text-2xl font-bold text-gray-800 mb-3">Professional Summary</h2>
            <p class="text-gray-700 leading-relaxed">
                Results-oriented Software Engineer with 5+ years of experience in developing high-quality software solutions. Proficient in full-stack development, specializing in Python, JavaScript, and cloud-based applications. Adept at collaborating with cross-functional teams to deliver scalable and efficient solutions.
            </p>
        </div>

        <!-- Key Skills Section -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
            <div class="bg-gray-50 p-6 rounded-lg shadow-sm">
                <h3 class="text-xl font-semibold text-indigo-600 mb-2">Technical Skills</h3>
                <ul class="list-disc list-inside text-gray-700">
                    <li>JavaScript, React.js, Node.js</li>
                    <li>Python, Django, Flask</li>
                    <li>SQL, NoSQL (MongoDB)</li>
                    <li>AWS, Azure, Docker</li>
                    <li>RESTful API Development</li>
                </ul>
            </div>
            <div class="bg-gray-50 p-6 rounded-lg shadow-sm">
                <h3 class="text-xl font-semibold text-indigo-600 mb-2">Soft Skills</h3>
                <ul class="list-disc list-inside text-gray-700">
                    <li>Agile Methodologies</li>
                    <li>Team Collaboration</li>
                    <li>Problem-Solving</li>
                    <li>Effective Communication</li>
                    <li>Time Management</li>
                </ul>
            </div>
        </div>

        <!-- Work Experience Section -->
        <div class="bg-indigo-100 p-6 mb-6 rounded-lg shadow-sm">
            <h2 class="text-2xl font-bold text-gray-800 mb-3">Work Experience</h2>

            <div class="mb-6">
                <h3 class="text-lg font-semibold text-indigo-700">Software Engineer</h3>
                <p class="text-gray-600">ABC Technologies, Seattle, WA</p>
                <p class="text-gray-500">August 2019 - Present</p>
                <ul class="list-disc list-inside text-gray-700">
                    <li>Developed and deployed web applications using React.js and Node.js, improving performance by 30%.</li>
                    <li>Designed REST APIs for internal services, reducing development time by 20% for the engineering team.</li>
                    <li>Collaborated with cross-functional teams to implement DevOps practices, achieving faster deployment cycles.</li>
                </ul>
            </div>

            <div>
                <h3 class="text-lg font-semibold text-indigo-700">Junior Software Engineer</h3>
                <p class="text-gray-600">XYZ Corp, Seattle, WA</p>
                <p class="text-gray-500">June 2016 - July 2019</p>
                <ul class="list-disc list-inside text-gray-700">
                    <li>Assisted in the development of scalable microservices architecture using Docker and AWS Lambda.</li>
                    <li>Collaborated with senior engineers to write clean, maintainable code using Python and Django.</li>
                    <li>Managed cloud infrastructure, ensuring 99.9% uptime for critical services.</li>
                </ul>
            </div>
        </div>

        <!-- Education Section -->
        <div class="bg-gray-50 p-6 rounded-lg shadow-sm">
            <h2 class="text-2xl font-bold text-gray-800 mb-3">Education</h2>
            <p class="text-lg font-semibold text-gray-900">Bachelor of Science in Computer Science</p>
            <p class="text-gray-600">University of Washington, Seattle</p>
            <p class="text-gray-500">Graduated: 2016</p>
        </div>

        <!-- Certifications Section -->
        <div class="bg-gray-50 p-6 mt-6 rounded-lg shadow-sm">
            <h2 class="text-2xl font-bold text-gray-800 mb-3">Certifications</h2>
            <p class="text-gray-700">AWS Certified Solutions Architect - Associate (2021)</p>
            <p class="text-gray-700">Certified Kubernetes Administrator (CKA) (2020)</p>
        </div>
    </div>
</body>
</html>
