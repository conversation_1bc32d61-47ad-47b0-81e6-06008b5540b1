import { useState, useRef } from 'react';

export interface AchievementType {
  title: string;
  tasks: string[];
  date?: string;
}

const useAcheivement = (initialAchievements: AchievementType[]) => {
  const [achievements, setAchievements] = useState<AchievementType[]>(initialAchievements);
  const taskRefs = useRef<HTMLLIElement[][]>([]); // Array to store refs for each task list item
  const achievementRefs = useRef<{ [key: string]: HTMLElement | null }>({}); // Store refs for achievement fields

  const setCursorToEnd = (el: HTMLLIElement): void => {
    const range = document.createRange();
    const sel = window.getSelection();
    if (!sel) return;
    
    range.selectNodeContents(el);
    range.collapse(false);
    sel.removeAllRanges();
    sel.addRange(range);
    el.focus();
  };

  const handleTaskKeyDown = (e: React.KeyboardEvent<HTMLLIElement>, achievementIndex: number, taskIndex: number): void => {
    const taskText = taskRefs.current[achievementIndex]?.[taskIndex]?.textContent?.trim() || '';
    
    if (e.key === 'Enter') {
      e.preventDefault();
      const newAchievements = [...achievements];
      newAchievements[achievementIndex].tasks.splice(taskIndex + 1, 0, ''); // Add new task
      setAchievements(newAchievements);
      setTimeout(() => {
        setCursorToEnd(taskRefs.current[achievementIndex][taskIndex + 1]); // Move cursor to new task
      }, 0);
    } else if (e.key === 'Backspace' && taskText === '') {
      e.preventDefault();
      if (achievements[achievementIndex].tasks.length > 1) {
        const newAchievements = [...achievements];
        newAchievements[achievementIndex].tasks.splice(taskIndex, 1); // Remove task
        setAchievements(newAchievements);
        setTimeout(() => {
          const prevIndex = Math.max(0, taskIndex - 1);
          setCursorToEnd(taskRefs.current[achievementIndex][prevIndex]); // Move cursor to previous task
        }, 0);
      }
    } else {
      // Ensure the cursor stays where the user left it
      setTimeout(() => {
        const sel = window.getSelection();
        if (!sel || sel.rangeCount === 0) return;
        
        const range = sel.getRangeAt(0);
        if (range && range.startOffset !== range.endOffset) {
          return;
        }
        setCursorToEnd(taskRefs.current[achievementIndex][taskIndex]);
      }, 0);
    }
  };

  const handleTaskChange = (e: React.FormEvent<HTMLLIElement>, achievementIndex: number, taskIndex: number): void => {
    const newAchievements = [...achievements];
    newAchievements[achievementIndex].tasks[taskIndex] = e.currentTarget.textContent || '';
    setAchievements(newAchievements);
  };

  const handleAchievementFieldChange = (
    e: React.FormEvent<HTMLElement>,
    achievementIndex: number,
    field: keyof AchievementType
  ): void => {
    const newAchievements = [...achievements];
    const value = e.currentTarget.textContent || '';
    
    if (field === 'title') {
      newAchievements[achievementIndex].title = value;
    } else if (field === 'date') {
      newAchievements[achievementIndex].date = value;
    }
    
    setAchievements(newAchievements);
  };

  const handleAddAchievement = (): void => {
    setAchievements([
      ...achievements,
      {
        title: 'New Achievement',
        tasks: ['Achievement description'],
        date: 'Date',
      },
    ]);
  };

  const handleRemoveAchievement = (achievementIndex: number): void => {
    const newAchievements = [...achievements];
    newAchievements.splice(achievementIndex, 1);
    setAchievements(newAchievements);
  };

  return {
    achievements,
    handleTaskKeyDown,
    handleTaskChange,
    handleAchievementFieldChange,
    handleAddAchievement,
    handleRemoveAchievement,
    taskRefs,
    achievementRefs,
  };
};

export default useAcheivement;