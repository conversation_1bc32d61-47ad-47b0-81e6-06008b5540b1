import { useState, useRef } from 'react';

export interface KeySkillProps {
  skills: string[];
}
const useKeySkill = (initialSkills: string[]) => {
  const [skills, setSkills] = useState<string[]>(initialSkills);
  const skillRefs = useRef<HTMLLIElement[]>([]); // Store refs for skill list items
  
  const setCursorToEnd = (el: HTMLLIElement): void => {
    const range = document.createRange();
    const sel = window.getSelection();
    if (!sel) return;
    
    range.selectNodeContents(el);
    range.collapse(false); // Collapses range to the end of the content
    sel.removeAllRanges();
    sel.addRange(range);
    el.focus();
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLLIElement>, index: number): void => {
    const skillText = skillRefs.current[index]?.textContent?.trim() || '';
    
    if (e.key === 'Enter') {
      e.preventDefault();
      const newSkills = [...skills];
      newSkills.splice(index + 1, 0, ''); // Insert new bullet
      setSkills(newSkills);
      setTimeout(() => {
        setCursorToEnd(skillRefs.current[index + 1]); // Move cursor to the new bullet
      }, 0);
    } else if (e.key === 'Backspace' && skillText === '') {
      e.preventDefault();
      if (skills.length > 1) {
        const newSkills = [...skills];
        newSkills.splice(index, 1); // Remove the current bullet
        setSkills(newSkills);
        setTimeout(() => {
          const prevIndex = Math.max(0, index - 1);
          setCursorToEnd(skillRefs.current[prevIndex]); // Move cursor to the previous bullet
        }, 0);
      }
    } else {
      // Ensure the cursor stays where the user left it
      setTimeout(() => {
        const sel = window.getSelection();
        if (!sel || sel.rangeCount === 0) return;
        
        const range = sel.getRangeAt(0); // Get the current selection range
        if (range && range.startOffset !== range.endOffset) {
          // Do nothing if the cursor is already positioned
          return;
        }
        // Otherwise, move the cursor to the correct spot
        setCursorToEnd(skillRefs.current[index]);
      }, 0);
    }
  };
  
  const handleChange = (e: React.FormEvent<HTMLLIElement>, index: number): void => {
    const newSkills = [...skills];
    newSkills[index] = e.currentTarget.textContent || '';
    setSkills(newSkills);
  };
  
  return {
    skills,
    handleKeyDown,
    handleChange,
    skillRefs,
  };
};

export default useKeySkill;