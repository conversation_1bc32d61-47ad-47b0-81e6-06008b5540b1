import { useState, useRef } from 'react';

export interface ProjectType {
  name: {
    text: string;
    url?: string;
  };
  description: string;
  technologies: string[];
  dates: string;
}

const useProject = (initialProjects: ProjectType[]) => {
  const [projects, setProjects] = useState<ProjectType[]>(initialProjects);
  const techRefs = useRef<HTMLLIElement[][]>([]); // Array to store refs for each technology list item
  const projectRefs = useRef<{ [key: string]: HTMLElement | null }>({}); // Store refs for project fields

  const setCursorToEnd = (el: HTMLLIElement): void => {
    const range = document.createRange();
    const sel = window.getSelection();
    if (!sel) return;
    
    range.selectNodeContents(el);
    range.collapse(false);
    sel.removeAllRanges();
    sel.addRange(range);
    el.focus();
  };

  const handleTechKeyDown = (e: React.KeyboardEvent<HTMLLIElement>, projectIndex: number, techIndex: number): void => {
    const techText = techRefs.current[projectIndex]?.[techIndex]?.textContent?.trim() || '';
    
    if (e.key === 'Enter') {
      e.preventDefault();
      const newProjects = [...projects];
      newProjects[projectIndex].technologies.splice(techIndex + 1, 0, ''); // Add new technology
      setProjects(newProjects);
      setTimeout(() => {
        setCursorToEnd(techRefs.current[projectIndex][techIndex + 1]); // Move cursor to new tech
      }, 0);
    } else if (e.key === 'Backspace' && techText === '') {
      e.preventDefault();
      if (projects[projectIndex].technologies.length > 1) {
        const newProjects = [...projects];
        newProjects[projectIndex].technologies.splice(techIndex, 1); // Remove technology
        setProjects(newProjects);
        setTimeout(() => {
          const prevIndex = Math.max(0, techIndex - 1);
          setCursorToEnd(techRefs.current[projectIndex][prevIndex]); // Move cursor to previous tech
        }, 0);
      }
    } else {
      // Ensure the cursor stays where the user left it
      setTimeout(() => {
        const sel = window.getSelection();
        if (!sel || sel.rangeCount === 0) return;
        
        const range = sel.getRangeAt(0);
        if (range && range.startOffset !== range.endOffset) {
          return;
        }
        setCursorToEnd(techRefs.current[projectIndex][techIndex]);
      }, 0);
    }
  };

  const handleTechChange = (e: React.FormEvent<HTMLLIElement>, projectIndex: number, techIndex: number): void => {
    const newProjects = [...projects];
    newProjects[projectIndex].technologies[techIndex] = e.currentTarget.textContent || '';
    setProjects(newProjects);
  };

  const handleProjectFieldChange = (
    e: React.FormEvent<HTMLElement>,
    projectIndex: number,
    field: keyof ProjectType | 'name.text' | 'name.url'
  ): void => {
    const newProjects = [...projects];
    const value = e.currentTarget.textContent || '';
    
    if (field === 'name.text') {
      newProjects[projectIndex].name.text = value;
    } else if (field === 'name.url') {
      newProjects[projectIndex].name.url = value;
    } else if (field === 'description') {
      newProjects[projectIndex].description = value;
    } else if (field === 'dates') {
      newProjects[projectIndex].dates = value;
    }
    
    setProjects(newProjects);
  };

  const handleAddProject = (): void => {
    setProjects([
      ...projects,
      {
        name: {
          text: 'New Project',
          url: '',
        },
        description: 'Project description',
        technologies: ['Technology'],
        dates: 'Date Range',
      },
    ]);
  };

  const handleRemoveProject = (projectIndex: number): void => {
    const newProjects = [...projects];
    newProjects.splice(projectIndex, 1);
    setProjects(newProjects);
  };

  return {
    projects,
    handleTechKeyDown,
    handleTechChange,
    handleProjectFieldChange,
    handleAddProject,
    handleRemoveProject,
    techRefs,
    projectRefs,
  };
};

export default useProject;