import { useState, useRef } from "react";

export interface CertificationType {
  name?: string;
  institution?: string;
  year?: string;
}

export type CertificationEntry = string | CertificationType;

const useCertification = (initialCertifications: CertificationEntry[]) => {
  const [certifications, setCertifications] = useState<CertificationEntry[]>(initialCertifications);
  const certRefs = useRef<HTMLLIElement[]>([]); // Store refs for certification list items
  const cursorPosition = useRef<number | null>(null); // Store cursor position

  const setCursorToEnd = (el: HTMLLIElement): void => {
    const range = document.createRange();
    const sel = window.getSelection();
    if (!sel) return;
    
    range.selectNodeContents(el);
    range.collapse(false); // Move cursor to the end of the text
    sel.removeAllRanges();
    sel.addRange(range);
    el.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLLIElement>, index: number): void => {
    const certText = certRefs.current[index]?.textContent?.trim() || '';
    
    if (e.key === "Enter") {
      e.preventDefault();
      const newCertifications = [...certifications];
      newCertifications.splice(index + 1, 0, ""); // Insert new certification
      setCertifications(newCertifications);
      setTimeout(() => {
        setCursorToEnd(certRefs.current[index + 1]); // Move cursor to the new certification
      }, 0);
    } else if (e.key === "Backspace" && certText === "") {
      e.preventDefault();
      if (certifications.length > 1) {
        const newCertifications = [...certifications];
        newCertifications.splice(index, 1); // Remove the current certification
        setCertifications(newCertifications);
        setTimeout(() => {
          const prevIndex = Math.max(0, index - 1);
          setCursorToEnd(certRefs.current[prevIndex]); // Move cursor to the previous certification
        }, 0);
      }
    } else {
      // Ensure the cursor stays where the user left it
      setTimeout(() => {
        const sel = window.getSelection();
        if (!sel || sel.rangeCount === 0) return;
        
        const range = sel.getRangeAt(0); // Get the current selection range
        if (range && range.startOffset !== range.endOffset) {
          // Do nothing if the cursor is already positioned
          return;
        }
        // Otherwise, move the cursor to the correct spot
        setCursorToEnd(certRefs.current[index]);
      }, 0);
    }
  };

  const handleChange = (e: React.FormEvent<HTMLLIElement>, index: number): void => {
    const newCertifications = [...certifications];
    newCertifications[index] = e.currentTarget.textContent || '';
    setCertifications(newCertifications);
    
    const sel = window.getSelection();
    if (sel && sel.rangeCount > 0) {
      cursorPosition.current = sel.getRangeAt(0).endOffset; // Store cursor position
    }
  };

  return {
    certifications,
    setCertifications,
    handleKeyDown,
    handleChange,
    certRefs,
  };
};

export default useCertification;