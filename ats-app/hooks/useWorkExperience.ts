import { useState, useRef } from 'react';
import { WorkExperience as WorkExperienceItem } from "@/components/resumes/resumeTypes";


const useWorkExperience = (initialWorkExperience: WorkExperienceItem[]) => {
  const [workExperience, setWorkExperience] = useState<WorkExperienceItem[]>(initialWorkExperience);
  const taskRefs = useRef<HTMLLIElement[][]>([]); // Array to store refs for each task list item

  const setCursorToEnd = (el: HTMLLIElement): void => {
    const range = document.createRange();
    const sel = window.getSelection();
    if (!sel) return;
    
    range.selectNodeContents(el);
    range.collapse(false);
    sel.removeAllRanges();
    sel.addRange(range);
    el.focus();
  };

  const handleTaskKeyDown = (e: React.KeyboardEvent<HTMLLIElement>, jobIndex: number, taskIndex: number): void => {
    const taskText = taskRefs.current[jobIndex][taskIndex].textContent?.trim() || ''; // Get task text content
    
    if (e.key === 'Enter') {
      e.preventDefault();
      const newExperience = [...workExperience];
      newExperience[jobIndex]?.tasks?.splice(taskIndex + 1, 0, ''); // Add new task
      setWorkExperience(newExperience);
      setTimeout(() => {
        setCursorToEnd(taskRefs.current[jobIndex][taskIndex + 1]); // Move cursor to new task
      }, 0);
    } else if (e.key === 'Backspace' && taskText === '') {
      e.preventDefault();
      if ((workExperience[jobIndex]?.tasks ?? []).length > 1) {
        const newExperience = [...workExperience];
        newExperience[jobIndex]?.tasks?.splice(taskIndex, 1); // Remove task if tasks exist
        setWorkExperience(newExperience);
        setTimeout(() => {
          const prevIndex = Math.max(0, taskIndex - 1);
          setCursorToEnd(taskRefs.current[jobIndex][prevIndex]); // Move cursor to previous task
        }, 0);
      }
    } else {
      // Ensure the cursor stays where the user left it
      setTimeout(() => {
        const sel = window.getSelection();
        if (!sel || sel.rangeCount === 0) return;
        
        const range = sel.getRangeAt(0); // Get the current selection range
        if (range && range.startOffset !== range.endOffset) {
          // Do nothing if the cursor is already positioned
          return;
        }
        // Otherwise, move the cursor to the correct spot
        setCursorToEnd(taskRefs.current[jobIndex][taskIndex]);
      }, 0);
    }
  };

  const handleTaskChange = (e: React.FormEvent<HTMLLIElement>, jobIndex: number, taskIndex: number): void => {
    const newExperience = [...workExperience];
    if (newExperience[jobIndex] && newExperience[jobIndex].tasks) {
      newExperience[jobIndex].tasks[taskIndex] = e.currentTarget.textContent || '';
    }
    setWorkExperience(newExperience);
  };

  const handleAddWorkExperience = (): void => {
    setWorkExperience([
      ...workExperience,
      {
        title: 'New Job Title',
        company:{text: 'New Company'},
        location: 'Location',
        period: 'Period',
        tasks: ['New Task'],
      },
    ]);
  };

  const handleRemoveWorkExperience = (jobIndex: number): void => {
    const newExperience = [...workExperience];
    newExperience.splice(jobIndex, 1); // Remove job entry
    setWorkExperience(newExperience);
  };

  return {
    workExperience,
    handleTaskKeyDown,
    handleTaskChange,
    handleAddWorkExperience,
    handleRemoveWorkExperience,
    taskRefs,
  };
};

export default useWorkExperience;