"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, ArrowRight, Download, Eye } from "lucide-react";
import { useState } from "react";

export default function ATSComparisonDisplay({
  comparisonData,
  onViewTailoredResume,
  onDownloadTailoredResume,
}) {
  const [showDetailedComparison, setShowDetailedComparison] = useState(false);
  
  const {
    original_ats_score,
    tailored_ats_score,
    improvement_summary,
    tailored_resume
  } = comparisonData;

  // Helper function to get improvement status
  const getImprovementStatus = (improvement) => {
    if (improvement > 10) return { color: 'text-green-600', icon: TrendingUp, text: 'Excellent Improvement' };
    if (improvement > 5) return { color: 'text-blue-600', icon: TrendingUp, text: 'Good Improvement' };
    if (improvement > 0) return { color: 'text-yellow-600', icon: TrendingUp, text: 'Minor Improvement' };
    return { color: 'text-red-600', icon: TrendingDown, text: 'No Improvement' };
  };

  // Helper function to get status color and text
  const getScoreStatus = (score) => {
    if (score >= 80) return { color: 'bg-green-100 text-green-800', text: 'Excellent' };
    if (score >= 70) return { color: 'bg-blue-100 text-blue-800', text: 'Good' };
    if (score >= 60) return { color: 'bg-yellow-100 text-yellow-800', text: 'Fair' };
    return { color: 'bg-red-100 text-red-800', text: 'Needs Work' };
  };

  const improvementStatus = getImprovementStatus(improvement_summary.score_improvement);
  const originalStatus = getScoreStatus(improvement_summary.original_score);
  const tailoredStatus = getScoreStatus(improvement_summary.tailored_score);

  // Enhanced sections for detailed comparison
  const enhancedSections = [
    { key: 'experience_level_match', label: 'Experience Level Match' },
    { key: 'skills_match', label: 'Skills Match' },
    { key: 'education_match', label: 'Education Match' },
    { key: 'job_specific_keywords', label: 'Job-Specific Keywords' },
    { key: 'achievements_responsibilities', label: 'Achievements & Responsibilities' },
    { key: 'industry_relevance', label: 'Industry Relevance' },
    { key: 'certifications_training', label: 'Certifications & Training' }
  ];

  return (
    <div className="space-y-6">
      {/* Header with Overall Comparison */}
      <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <improvementStatus.icon className={`w-6 h-6 ${improvementStatus.color}`} />
              <span>ATS Score Improvement Analysis</span>
            </div>
            <Badge className={improvementStatus.color.replace('text-', 'bg-').replace('-600', '-100 text-') + improvementStatus.color.replace('text-', '-800')}>
              {improvementStatus.text}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Original Score */}
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-2">Original Score</div>
              <div className="text-3xl font-bold text-gray-700 mb-2">
                {improvement_summary.original_score}/100
              </div>
              <Badge className={originalStatus.color}>
                {originalStatus.text}
              </Badge>
              <Progress value={improvement_summary.original_score} className="w-full mt-3" />
            </div>

            {/* Improvement Arrow */}
            <div className="flex items-center justify-center">
              <div className="text-center">
                <ArrowRight className="w-8 h-8 text-primary mx-auto mb-2" />
                <div className={`text-lg font-semibold ${improvementStatus.color}`}>
                  {improvement_summary.score_improvement > 0 ? '+' : ''}{improvement_summary.score_improvement} points
                </div>
                <div className="text-sm text-gray-500">Improvement</div>
              </div>
            </div>

            {/* Tailored Score */}
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-2">Tailored Score</div>
              <div className="text-3xl font-bold text-primary mb-2">
                {improvement_summary.tailored_score}/100
              </div>
              <Badge className={tailoredStatus.color}>
                {tailoredStatus.text}
              </Badge>
              <Progress value={improvement_summary.tailored_score} className="w-full mt-3" />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 mt-6 justify-center">
            <Button 
              onClick={onViewTailoredResume}
              className="bg-primary hover:bg-primary/90"
            >
              <Eye className="w-4 h-4 mr-2" />
              View Tailored Resume
            </Button>
            {/* <Button 
              onClick={onDownloadTailoredResume}
              variant="outline"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Resume
            </Button> */}
            <Button 
              onClick={() => setShowDetailedComparison(!showDetailedComparison)}
              variant="outline"
            >
              {showDetailedComparison ? 'Hide' : 'Show'} Detailed Comparison
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Improvements Summary */}
      {(tailored_ats_score.top_priorities || tailored_ats_score.quick_wins) && (
        <Card>
          <CardHeader>
            <CardTitle>What Was Improved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {tailored_ats_score.top_priorities && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                    <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                    Major Improvements Made
                  </h4>
                  <ul className="space-y-2">
                    {tailored_ats_score.top_priorities.slice(0, 3).map((priority, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start">
                        <span className="text-green-500 mr-2">✓</span>
                        {priority}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {tailored_ats_score.quick_wins && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Quick Wins Applied
                  </h4>
                  <ul className="space-y-2">
                    {tailored_ats_score.quick_wins.slice(0, 3).map((win, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start">
                        <span className="text-blue-500 mr-2">⚡</span>
                        {win}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Section-by-Section Comparison */}
      {showDetailedComparison && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Section Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {enhancedSections.map((section) => {
                const originalSection = original_ats_score[section.key];
                const tailoredSection = tailored_ats_score[section.key];
                
                if (!originalSection || !tailoredSection) return null;

                const scoreDiff = tailoredSection.score - originalSection.score;
                const diffColor = scoreDiff > 0 ? 'text-green-600' : scoreDiff < 0 ? 'text-red-600' : 'text-gray-600';

                return (
                  <div key={section.key} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">{section.label}</h4>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-500">
                          {originalSection.score} → {tailoredSection.score}
                        </span>
                        <span className={`text-sm font-medium ${diffColor}`}>
                          {scoreDiff > 0 ? '+' : ''}{scoreDiff}
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="text-xs font-medium text-gray-500 mb-1">BEFORE</div>
                        <Progress value={originalSection.score} className="mb-2" />
                        <p className="text-sm text-gray-600">{originalSection.feedback}</p>
                      </div>
                      <div>
                        <div className="text-xs font-medium text-gray-500 mb-1">AFTER</div>
                        <Progress value={tailoredSection.score} className="mb-2" />
                        <p className="text-sm text-gray-600">{tailoredSection.feedback}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
