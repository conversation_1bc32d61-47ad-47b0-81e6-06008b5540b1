import React, { useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Briefcase } from "lucide-react";
import { useState } from "react";
import { Job } from "@/app/(in)/job-search/page";
import Link from "next/link";
import { createClient } from "@/utils/supabase/client";

const JobSection = ({ jobPage }: { jobPage: boolean }) => {
  const [jobSearchQuery, setJobSearchQuery] = useState<string>("");
  const [searchResults, setSearchResults] = useState<Job[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [, setError] = useState<string>("");
  const [accessToken, setAccessToken] = useState<string | null>(null);

  const supabase = createClient();
  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  const handleJobSearch = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSearching(true);


    setIsSearching(true);
    setError("");

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/api/job-search`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify({ query: jobSearchQuery }),
        }
      );

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }

      const data = await response.json();
      setSearchResults(data.jobs || []);
    } catch (err) {
      setError("Failed to upload resume and fetch jobs. Please try again.");
      console.error(err);
    } finally {
      setIsSearching(false);
    }
    // setSearchResults(mockResults);
    setIsSearching(false);
    // }, 1000);
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <h2 className="font-semibold text-lg mb-4">Job Search</h2>
        <form onSubmit={handleJobSearch} className="mb-4">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4" />
              <Input
                placeholder="Search jobs (e.g., software engineering job with CTC 35 Lpa in Banglore.)"
                className="pl-10"
                value={jobSearchQuery}
                onChange={(e) => setJobSearchQuery(e.target.value)}
              />
            </div>
            <Button type="submit" disabled={isSearching}>
              {isSearching ? "Searching..." : "Search"}
            </Button>
          </div>
        </form>

        {searchResults.length > 0 && (
          <div className="space-y-4">
            {searchResults.map((job) => (
              <div
                key={job.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-lg">{job.job_title}</h3>
                    <p className="text-gray-600">
                      {job.company} • {job.location}
                    </p>
                    <p className="mt-2 text-sm">{job.summary}</p>
                  </div>
                  <div className="flex flex-col gap-2">
                    {job.apply_url ? (
                      <a href={job.apply_url} target="_blank">
                        <Button size="sm">Apply</Button>
                      </a>
                    ) : (
                      <Button size="sm" disabled>
                        Apply
                      </Button>
                    )}
                    {/* <Button size="sm" variant="outline">
                      Check ATS
                    </Button> */}
                  </div>
                </div>
              </div>
            ))}
            {!jobPage && (
              <div className="flex justify-end">
                <Link href="/job-search">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}

        {jobSearchQuery && searchResults.length === 0 && !isSearching && (
          <div className="text-center py-6">
            <p className="text-gray-500">
              No jobs found matching your search criteria.
            </p>
            <p className="text-sm text-gray-400 mt-1">
              Try modifying your search terms or using the advanced search.
            </p>
          </div>
        )}

        {!jobSearchQuery && searchResults.length === 0 && (
          <div className="text-center py-6 px-4">
            <Briefcase className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <h3 className="font-medium text-lg">Find your next opportunity</h3>
            <p className="text-gray-500 mt-2">
              Enter details like job title, salary expectations, or company name
              to find relevant openings.
            </p>
            <div className="mt-4">
              <p className="text-sm text-gray-400">Try example searches:</p>
              <div className="flex flex-wrap gap-2 justify-center mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setJobSearchQuery("Frontend Developer 25lpa")}
                >
                  Frontend Developer 25lpa
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setJobSearchQuery("Data scientist remote")}
                >
                  Data scientist remote
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setJobSearchQuery("Software engineering job with 35lpa cts")
                  }
                >
                  Software engineering job with CTC 35 Lpa in Banglore.
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default JobSection;
