import React, { useEffect, useState } from 'react';
import { createClient } from "@/utils/supabase/client";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";

interface Stats {
  atsScore: number;
  jobsMatched: number;
  tailored: number;
  coverLetters: number;
}

interface StatsResponse {
  stats: Stats;
}

const Stats = () => {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [resumeStats, setResumeStats] = useState<Stats>({
    atsScore: 0,
    jobsMatched: 0,
    tailored: 0,
    coverLetters: 0
  });
  const [loading, setLoading] = useState<boolean>(true);

  const supabase = createClient();

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  useEffect(() => {
    const fetchActivities = async () => {
      if (!accessToken) return;

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/api/stats`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }

        const data: StatsResponse = await response.json();
        setResumeStats(data.stats);
      } catch (err) {
        console.error("Error fetching activities:", err);
      } finally {
        setLoading(false);
      }
    };

    if (accessToken) {
      fetchActivities();
    }
  }, [accessToken]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card
        className={`${
          resumeStats.atsScore >= 80
            ? ""
            : resumeStats.atsScore >= 50
            ? "border-yellow-200"
            : "border-orange-200"
        } border-2 ${loading ? "opacity-70" : ""}`}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <span
              className={`${
                resumeStats.atsScore >= 80
                  ? "text-primary"
                  : resumeStats.atsScore >= 50
                  ? "text-yellow-500"
                  : "text-orange-500"
              } ${loading ? "text-gray-400" : ""}`}
            >
              Latest ATS Score
            </span>
            <span
              className={`text-lg font-bold ${
                resumeStats.atsScore >= 80
                  ? ""
                  : resumeStats.atsScore >= 50
                  ? "text-yellow-500"
                  : "text-orange-500"
              } ${loading ? "text-gray-400" : ""}`}
            >
              {resumeStats.atsScore}%
            </span>
          </div>
          <Progress
            indicatorColor={
              loading
                ? "bg-gray-300"
                : resumeStats.atsScore >= 80
                ? "bg-primary"
                : resumeStats.atsScore >= 50
                ? "bg-yellow-500"
                : "bg-orange-500"
            }
            value={resumeStats.atsScore}
            className="mt-2"
          />
        </CardContent>
      </Card>

      <Card className={loading ? "opacity-70" : ""}>
        <CardContent className="p-4">
          <div className="flex justify-between">
            <span className={loading ? "text-gray-400" : ""}>Tailored Resume</span>
            <span className={`text-2xl font-bold ${loading ? "text-gray-400" : ""}`}>
              {resumeStats.tailored}
            </span>
          </div>
        </CardContent>
      </Card>

      <Card className={loading ? "opacity-70" : ""}>
        <CardContent className="p-4">
          <div className="flex justify-between">
            <span className={loading ? "text-gray-400" : ""}>Jobs Matched</span>
            <span className={`text-2xl font-bold ${loading ? "text-gray-400" : ""}`}>
              {resumeStats.jobsMatched}
            </span>
          </div>
        </CardContent>
      </Card>

      <Card className={loading ? "opacity-70" : ""}>
        <CardContent className="p-4">
          <div className="flex justify-between">
            <span className={loading ? "text-gray-400" : ""}>Cover Letters</span>
            <span className={`text-2xl font-bold ${loading ? "text-gray-400" : ""}`}>
              {resumeStats.coverLetters}
            </span>
          </div>
        </CardContent>
      </Card>
      
      {loading && (
        <div className="col-span-1 md:col-span-2 lg:col-span-4 text-center text-sm text-gray-400 mt-2">
          Loading stats...
        </div>
      )}
    </div>
  );
};

export default Stats;