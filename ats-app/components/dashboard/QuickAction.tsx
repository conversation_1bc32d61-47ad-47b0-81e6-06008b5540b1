import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileUp, FileText } from "lucide-react";

const QuickAction = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <Card>
        <CardContent className="p-6 flex flex-col items-center text-center">
          <FileUp className="h-8 w-8 mb-2 text-blue-500" />
          <h3 className="font-semibold mb-2">ATS Check</h3>
          <p className="text-sm text-gray-500 mb-4">
            Upload your resume to check ATS compatibility score and get
            improvement tips
          </p>
          <Button
            onClick={() => (window.location.href = "/ats")}
            className="w-full"
          >
            Check Resume
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 flex flex-col items-center text-center">
          <FileText className="h-8 w-8 mb-2 text-blue-500" />
          <h3 className="font-semibold mb-2">Cover Letter</h3>
          <p className="text-sm text-gray-500 mb-4">
            Generate personalized cover letters tailored to specific job
            descriptions
          </p>
          <Button
            onClick={() => (window.location.href = "/cover-letter")}
            className="w-full"
          >
            Create Cover Letter
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuickAction;
