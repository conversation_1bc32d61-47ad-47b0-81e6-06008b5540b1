import React, { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  FileUp,
  FileText,
  BarChart2,
  Briefcase,
  Clock,
  FileSearch,
} from "lucide-react";
import Link from "next/link";

interface ActivityDetails {
  atsScore?: number;
  feedback?: string; // Added for resume review feedback
}

interface Activity {
  id: string;
  type: string;
  title: string;
  date: string;
  details: ActivityDetails;
  activity_id: string;
}

const RecentActivities = () => {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);

  const supabase = createClient();

  // Utility to normalize activity type from API
  const normalizeType = (type: string): string => type.replace(/-/g, "_");

  // Get icon based on activity type - Added resume_reviewed icon
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "resume_scan":
      case "resume_scanned":
      case "resume_tailored":
        return <FileUp className="h-4 w-4 text-blue-500" />;
      case "resume_reviewed": // Added new activity type
        return <FileSearch className="h-4 w-4 text-teal-500" />;
      case "job_application":
      case "job_applied":
        return <Briefcase className="h-4 w-4 text-green-500" />;
      case "cover_letter":
        return <FileText className="h-4 w-4 text-purple-500" />;
      case "job_saved":
        return <BarChart2 className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format date to display in a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();

    if (date.toDateString() === now.toDateString()) {
      return `Today at ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })}`;
    }

    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday at ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })}`;
    }

    return (
      date.toLocaleDateString() +
      " at " +
      date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  useEffect(() => {
    const fetchActivities = async () => {
      if (!accessToken) return;

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/api/activities?mode=recent`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }

        const data = await response.json();
        const normalized = (data.activities || []).map(
          (activity: Activity) => ({
            ...activity,
            type: normalizeType(activity.type),
          })
        );

        setRecentActivities(normalized);
      } catch (err) {
        console.error("Error fetching activities:", err);
      }
    };

    if (accessToken) {
      fetchActivities();
    }
  }, [accessToken]);

  return (
    <Card className="h-full">
      <CardContent className="p-6 flex flex-col h-full">
        <div className="flex items-center justify-between mb-4">
          <h2 className="font-semibold text-lg">Recent Activities</h2>
          <Link href="/history">
            <Button variant="outline" size="sm">
              View All
            </Button>
          </Link>
        </div>

        <div className="space-y-3 overflow-y-auto flex-grow">
          {recentActivities.map((activity) => (
            <div
              key={activity.id}
              className="flex items-start gap-3 pb-3 border-b last:border-0"
            >
              <div className="bg-gray-100 p-2 rounded">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">{activity.title}</p>
                <div className="flex gap-2 items-center mt-1">
                  <p className="text-xs text-gray-500">
                    {formatDate(activity.date)}
                  </p>
                  {/* ATS Score display for scan and tailored */}
                  {(activity.type === "resume_scanned" ||
                    activity.type === "resume_tailored") &&
                    activity.details?.atsScore !== undefined && (
                      <span
                        className={`text-xs ${
                          activity.details.atsScore >= 80
                            ? "text-green-600 bg-green-50"
                            : activity.details.atsScore >= 50
                            ? "text-yellow-600 bg-yellow-50"
                            : "text-orange-600 bg-orange-50"
                        } px-2 py-0.5 rounded`}
                      >
                        ATS Score:{" "}
                        <span className="font-medium">
                          {activity.details.atsScore}%
                        </span>
                      </span>
                    )}
                  {/* Resume review badge */}
                  {activity.type === "resume_reviewed" && (
                    <span className="text-xs text-teal-600 bg-teal-50 px-2 py-0.5 rounded">
                      Review Complete
                    </span>
                  )}
                </div>
                {/* Show a preview of feedback for resume reviews */}
                {activity.type === "resume_reviewed" &&
                  activity.details?.feedback && (
                    <p className="text-xs text-gray-600 mt-1 line-clamp-1">
                      Feedback: {activity.details.feedback.substring(0, 60)}
                      {activity.details.feedback.length > 60 ? "..." : ""}
                    </p>
                  )}
              </div>
              <div>
                <Link href={`/details/${activity.activity_id}`}>
                  <Button variant="ghost" size="sm">
                    Details
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {recentActivities.length === 0 && (
          <div className="text-center py-6 flex-grow flex flex-col justify-center">
            <Clock className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">No recent activities to display</p>
            <p className="text-sm text-gray-400 mt-1">
              Activities like resume scans, reviews, and job applications will
              appear here
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RecentActivities;
