import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload } from "lucide-react";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";

const TailorResume = () => {
  const supabase = createClient();
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [jobDescription, setJobDescription] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    resume?: string;
    jobDescription?: string;
  }>({});

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  const validateForm = () => {
    const errors: { resume?: string; jobDescription?: string } = {};
    
    if (!resumeFile) {
      errors.resume = "Please upload a resume file";
    }
    
    if (!jobDescription.trim()) {
      errors.jobDescription = "Please provide a job description";
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setResumeFile(file);
      // Clear resume validation error when file is uploaded
      if (validationErrors.resume) {
        setValidationErrors(prev => ({ ...prev, resume: undefined }));
      }
      toast.success("Resume uploaded successfully");
    }
  };

  const handleJobDescriptionChange = (value: string) => {
    setJobDescription(value);
    // Clear job description validation error when user starts typing
    if (validationErrors.jobDescription && value.trim()) {
      setValidationErrors(prev => ({ ...prev, jobDescription: undefined }));
    }
  };

  const handleTailorResume = async () => {
    if (!validateForm()) {
      return;
    }

    if (!accessToken) {
      toast.error("Authentication required. Please log in.");
      return;
    }
    
    const formData = new FormData();
    formData.append("resume", resumeFile!);
    formData.append("job_description", jobDescription);
    formData.append("save_resume", "false");
    
    try {
      setIsLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/api/upload-resume`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          body: formData,
        }
      );

      if (response.status === 429) {
        toast.warning("Rate limit exceeded. Please try again later.");
        return;
      }

      if (!response.ok) {
        throw new Error("Request failed");
      }

      const data = await response.json();
      
      // Redirect to the generate-resume page with the resume ID
      if (data && data.resume_id) {
        window.location.href = `/generate-resume/${data.resume_id}`;
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error(error);
      toast.error("Error processing resume. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="h-full">
      <CardContent className="p-6 flex flex-col h-full">
        <div className="mb-4">
          <h2 className="font-semibold text-lg">
            Tailor Resume to Job Description
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Optimize your resume for specific job postings to increase
            interview chances
          </p>
        </div>
        
        <div className="grid gap-4 flex-grow">
          <div>
            <Label htmlFor="resume-upload" className="mb-2 text-primary block">
              Resume *
            </Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className={`w-auto ${
                  validationErrors.resume ? 'border-red-500' : ''
                }`}
                size="sm"
                onClick={() => document.getElementById("resume-upload")?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Resume
              </Button>
              <Input
                id="resume-upload"
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                onChange={handleResumeUpload}
              />
            </div>
            {resumeFile && (
              <p className="text-sm text-green-600 mt-2">
                ✓ {resumeFile.name} uploaded
              </p>
            )}
            {validationErrors.resume && (
              <p className="text-sm text-red-500 mt-2">
                {validationErrors.resume}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="job-description" className="mb-2 text-primary block">
              Job Description *
            </Label>
            <Textarea
              id="job-description"
              placeholder="Paste the job description here..."
              value={jobDescription}
              onChange={(e) => handleJobDescriptionChange(e.target.value)}
              className={`h-24 resize-none ${
                validationErrors.jobDescription ? 'border-red-500' : ''
              }`}
            />
            {validationErrors.jobDescription && (
              <p className="text-sm text-red-500 mt-2">
                {validationErrors.jobDescription}
              </p>
            )}
          </div>

          <Button
            onClick={handleTailorResume}
            disabled={isLoading}
            className="w-full mt-2"
          >
            {isLoading ? "Processing..." : "Tailor Resume"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default TailorResume;