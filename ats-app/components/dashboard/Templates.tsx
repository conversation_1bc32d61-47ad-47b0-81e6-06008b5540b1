import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../ui/card";
import Image from "next/image";
import { Button } from "../ui/button";

const Templates = () => {
  const resumeTemplates = [
    {
      id: 1,
      image: "/images/resume-thumbnails/classic-professional.png",
      caption: "Classic Professional",
      description: "A minimalist, ATS-friendly design with a single-column layout.",
      url: "/resume/templates/classic-professional",
    },
    {
      id: 2,
      image: "/images/resume-thumbnails/modern-two-column.png",
      caption: "Modern Two-Column",
      description: "A balanced two-column layout with structured sections.",
      url: "/resume/templates/modern-two-columns",
    },
    {
      id: 3,
      image: "/images/resume-thumbnails/creative-card-layout.png",
      caption: "Creative Card Layout",
      description: "A visually engaging template with card-style sections.",
      url: "/resume/templates/creative-card-layout",
    },
  ];

  return (
    <Card className="border shadow-sm mb-6">
      <CardHeader className="py-3 px-4 border-b">
        <CardTitle className="text-base font-medium">Resume Templates</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
          {resumeTemplates.map((template) => (
            <div 
              key={template.id}
              className="border rounded-md overflow-hidden hover:shadow-sm transition-all duration-300"
            >
              <div className="flex h-58">
                <div className="relative w-1/2 overflow-hidden">
                  <Image
                    src={template.image}
                    alt={template.caption}
                    width={290}
                    height={192}
                    className="object-cover w-full h-full transition-transform duration-300 hover:scale-105"
                  />
                </div>
                <div className="w-2/3 p-3 flex flex-col justify-between">
                  <div>
                    <h3 className="text-sm font-medium mb-1">{template.caption}</h3>
                    <p className="text-xs text-gray-500 line-clamp-2">{template.description}</p>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="text-xs h-7 px-3 cursor-pointer"
                      onClick={() => (window.location.href = template.url)}
                    >
                      View
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end ">
          <Button 
            variant="outline" 
            size="sm"
            className="text-sm cursor-pointer text-primary"
            onClick={() => (window.location.href = "/resume/templates")}
          >
            View All Templates
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default Templates;