"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, AlertCircle, Info } from "lucide-react";

export default function ATSScoreDisplay({
  atsResponse,
}) {
  const { final_ats_score, score_breakdown } = atsResponse.data || {};
  const { resume_authenticity } = atsResponse;

// Helper function to get status color and icon
  const getStatusInfo = (status) => {
    if (typeof status === 'string') {
      switch (status.toLowerCase()) {
        case 'excellent':
          return { color: 'bg-green-100 text-green-800', textColor: 'text-green-600' };
        case 'good':
          return { color: 'bg-blue-100 text-blue-800', textColor: 'text-blue-600' };
        case 'needs_improvement':
          return { color: 'bg-yellow-100 text-yellow-800', textColor: 'text-yellow-600' };
        case 'poor':
          return { color: 'bg-red-100 text-red-800', textColor: 'text-red-600' };
        default:
          return { color: 'bg-gray-100 text-gray-800', textColor: 'text-gray-600' };
      }
    }

  };

  // Check if we have the new enhanced format
  const isEnhancedFormat = atsResponse.overall_score !== undefined || atsResponse.job_title !== undefined;

  // Section mapping for enhanced format
  const enhancedSections = [
    { key: "experience_level_match", label: "Experience Level Match" },
    { key: "skills_match", label: "Skills Match" },
    { key: "education_match", label: "Education Match" },
    { key: "job_specific_keywords", label: "Job-Specific Keywords" },
    { key: "achievements_responsibilities", label: "Achievements & Responsibilities" },
    { key: "industry_relevance", label: "Industry Relevance" },
    { key: "certifications_training", label: "Certifications & Training" },
  ];

  if (!isEnhancedFormat) {
    // Legacy format display
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold mb-1">Final ATS Score</h2>
            <p className="text-4xl font-bold text-green-600">
              {final_ats_score}/100
            </p>
          </div>

        </div>

        <div>
          <h2 className="text-xl font-semibold mb-1">Resume Authenticity</h2>
          <p className="text-gray-700">{resume_authenticity}</p>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-3">Score Breakdown</h2>
          <div className="space-y-4">
            {score_breakdown.map((category, idx) => (
              <Card key={idx}>
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between">
                    <h3 className="text-lg font-semibold">{category.category}</h3>
                    <p className="text-sm text-gray-600">
                      Score: {category.adjusted_score} / Weight:{" "}
                      {category.weightage}
                    </p>
                  </div>
                  <p className="text-gray-700">
                    <strong>Explanation:</strong> {category.explanation}
                  </p>
                  {category.improvements.length > 0 && (
                    <div>
                      <strong className="block mb-1">Improvements:</strong>
                      <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                        {category.improvements.map((improvement, i) => (
                          <li key={i}>{improvement}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Enhanced format display
  return (
    <div className="space-y-6">
      {/* Header with Overall Score and Job Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <div>
                <span>{atsResponse.title || "ATS Score Analysis"}</span>
                {atsResponse.job_title && (
                  <div className="text-sm font-normal text-gray-500 mt-1">
                    {atsResponse.job_title}
                    {atsResponse.company_name && ` at ${atsResponse.company_name}`}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-3xl font-bold text-primary">
                  {atsResponse.overall_score || final_ats_score}/100
                </div>
                <div className="text-sm text-gray-500">ATS Score</div>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {atsResponse.experience_level && (
              <div>
                <span className="text-sm font-medium text-gray-500">Experience Level</span>
                <Badge variant="outline" className="ml-2 capitalize">
                  {atsResponse.experience_level.replace('_', ' ')}
                </Badge>
              </div>
            )}
            {atsResponse.job_match_percentage !== undefined && (
              <div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-medium text-gray-500">Job Match</span>
                  <Info
                    className="w-3 h-3 text-gray-400 hover:text-gray-600 cursor-help"
                    title="How well your resume matches this specific job's requirements, including skills, experience level, and qualifications. Higher percentage means better fit for this role."
                  />
                  <Badge variant="outline" className="ml-2">
                    {atsResponse.job_match_percentage}%
                  </Badge>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Top Priorities */}
      {atsResponse.top_priorities && atsResponse.top_priorities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2 text-red-500" />
              Top Priorities for This Job
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {atsResponse.top_priorities.map((priority, index) => (
                <li key={index} className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{priority}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Section-wise ATS Analysis */}
      <div className="grid gap-6">
        {enhancedSections.map((section) => {
          const sectionData = atsResponse[section.key];
          if (!sectionData || typeof sectionData !== 'object') return null;

          const statusInfo = getStatusInfo(sectionData.status);
          return (
            <Card key={section.key}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    {section.label}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={statusInfo.color}>
                      {sectionData.status?.replace('_', ' ').toUpperCase() || 'UNKNOWN'}
                    </Badge>
                    {sectionData.weightage && (
                      <Badge variant="outline">
                        {sectionData.weightage}% weight
                      </Badge>
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Progress Bar with Score */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">Job Match Score</span>
                    <span className="text-sm font-bold text-gray-900">{sectionData.score || 0}/100</span>
                  </div>
                  <Progress value={sectionData.score || 0} className="w-full" />
                </div>

                {/* Feedback */}
                {sectionData.feedback && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                      Job Alignment Analysis
                    </h4>
                    <p className="text-gray-600 leading-relaxed">{sectionData.feedback}</p>
                  </div>
                )}

                {/* Improvements */}
                {sectionData.improvements && sectionData.improvements.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                      How to Improve Job Match
                    </h4>
                    <div className="space-y-2">
                      {sectionData.improvements.map((improvement, index) => (
                        <div key={index} className="flex items-start bg-orange-50 p-3 rounded-md">
                          <span className="flex-shrink-0 w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                            {index + 1}
                          </span>
                          <span className="text-gray-700 leading-relaxed">{improvement}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Examples */}
                {sectionData.examples && sectionData.examples.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Examples
                    </h4>
                    <div className="bg-green-50 border border-green-200 rounded-md p-4">
                      <div className="space-y-3">
                        {sectionData.examples.map((example, index) => (
                          <div key={index} className="bg-white p-3 rounded border border-green-100">
                            <div className="flex items-start">
                              <span className="flex-shrink-0 w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium mr-2 mt-0.5">
                                ✓
                              </span>
                              <span className="text-sm text-gray-700 italic leading-relaxed">"{example}"</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Course Recommendations */}
                {sectionData.course_recommendations && sectionData.course_recommendations.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                      Recommended Courses
                      <Badge variant="outline" className="ml-2 bg-orange-100 text-orange-700 text-xs">
                        BETA
                      </Badge>
                    </h4>
                    <div className="space-y-3">
                      {sectionData.course_recommendations.map((course, index) => (
                        <div key={index} className="bg-purple-50 border border-purple-200 rounded-md p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h5 className="font-medium text-purple-900">{course.course_title}</h5>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className="text-xs">
                                {course.difficulty_level}
                              </Badge>
                              <Badge variant="outline" className="text-xs bg-purple-100 text-purple-700">
                                {course.priority} priority
                              </Badge>
                            </div>
                          </div>
                          <div className="space-y-2 text-sm">
                            <div className="flex items-center space-x-4 text-gray-600">
                              <span>📚 {course.platform}</span>
                              <span>⏱️ {course.estimated_duration}</span>
                            </div>
                            <p className="text-gray-700">
                              <strong>Skills:</strong> {course.skill_addressed}
                            </p>
                            <p className="text-gray-600 italic">{course.reason}</p>
                            <div className="flex items-center space-x-2">
                              <span className="text-gray-500 font-medium cursor-not-allowed">
                                View Course (Coming Soon)
                              </span>
                              <Badge variant="outline" className="text-xs bg-orange-100 text-orange-700">
                                BETA
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Wins and Strengths */}
      <div className="grid md:grid-cols-2 gap-6">
        {atsResponse.quick_wins && atsResponse.quick_wins.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
                Quick Wins
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {atsResponse.quick_wins.map((win, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></span>
                    <span className="text-gray-600">{win}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {atsResponse.strengths && atsResponse.strengths.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-blue-500" />
                Key Strengths
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {atsResponse.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="flex-shrink-0 w-4 h-4 text-green-500 mt-0.5 mr-2" />
                    <span className="text-gray-600">{strength}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Priority Courses Section */}
      {atsResponse.priority_courses && atsResponse.priority_courses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                📚
              </span>
              Priority Learning Recommendations
              <Badge variant="outline" className="ml-2">
                {atsResponse.total_recommended_courses || atsResponse.priority_courses.length} courses
              </Badge>
              <Badge variant="outline" className="ml-2 bg-orange-100 text-orange-700 text-xs">
                BETA
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {atsResponse.priority_courses.map((course, index) => (
                <div key={index} className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-purple-900 mb-1">{course.course_title}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                        <span className="flex items-center">
                          <span className="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
                          {course.platform}
                        </span>
                        <span>⏱️ {course.estimated_duration}</span>
                        <Badge variant="outline" className="text-xs">
                          {course.difficulty_level}
                        </Badge>
                      </div>
                    </div>
                    <Badge className={`ml-4 ${course.priority === 'high' ? 'bg-red-100 text-red-700' : 'bg-yellow-100 text-yellow-700'}`}>
                      {course.priority} priority
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium text-gray-700">Skills addressed:</span>
                      <span className="ml-1 text-gray-600">{course.skill_addressed}</span>
                    </div>
                    <p className="text-sm text-gray-600 leading-relaxed">{course.reason}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-gray-500 font-medium cursor-not-allowed text-sm">
                        Start Learning (Coming Soon)
                      </span>
                      <Badge variant="outline" className="text-xs bg-orange-100 text-orange-700">
                        BETA
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overall Summary and Resume Authenticity */}
      {(atsResponse.overall_summary || resume_authenticity) && (
        <Card>
          <CardHeader>
            <CardTitle>Summary & Next Steps</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {atsResponse.overall_summary && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Overall Assessment</h4>
                <p className="text-gray-600">{atsResponse.overall_summary}</p>
              </div>
            )}

            {resume_authenticity && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Resume Authenticity</h4>
                <p className="text-gray-600">{resume_authenticity}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
