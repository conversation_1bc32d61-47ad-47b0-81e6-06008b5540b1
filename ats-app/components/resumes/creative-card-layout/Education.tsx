import React, { useRef } from "react";

interface EducationProps {
  education: {
    degree?: string;
    institution?: { text?: string; url?: string };
    year?: string;
  }[];
}
// Define the type for education refs
interface EducationRefs {
  [key: string]: HTMLElement | null;
}

const Education: React.FC<EducationProps> = ({ education }) => {
  const educationRefs = useRef<EducationRefs>({}); // Store refs for each education field

  const handleEducationChange = (e: React.FormEvent<HTMLElement>, field: string) => {
    // Store the current cursor position
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    const cursorPos = range.endOffset;
    
    // Set the cursor position back after re-rendering
    setTimeout(() => {
      const el = educationRefs.current[field];
      if (!el || !el.childNodes[0]) return;
      
      const newRange = document.createRange();
      newRange.setStart(
        el.childNodes[0],
        Math.min(cursorPos, (el.childNodes[0] as Text).length)
      );
      newRange.collapse(true);
      
      selection.removeAllRanges();
      selection.addRange(newRange);
    }, 0);
  };

  return (
    <div className='mb-3 section'>
      {/* Education Header */}
      <div className=''>
        <h2 className='text-xl font-bold text-gray-800 mb-1'>Education</h2>
      </div>
      <div className='mb-2'>
        {/* Education Entries */}
        {education.map((edu, index) => (
          <div key={index} className='flex justify-between items-center'>
            <div>
              <div
                contentEditable='true'
                className='text-gray-700'
                onInput={(e) => handleEducationChange(e, `degree-${index}`)}
                suppressContentEditableWarning={true}
                ref={(el) => {
                  if (!educationRefs.current) educationRefs.current = {};
                  educationRefs.current[`degree-${index}`] = el;
                }}
              >
                <strong>{edu.degree}</strong>
              </div>
              <span
                contentEditable='true'
                className='text-gray-700'
                onInput={(e) => handleEducationChange(e, `institution-${index}`)}
                suppressContentEditableWarning={true}
                ref={(el) => {
                  if (!educationRefs.current) educationRefs.current = {};
                  educationRefs.current[`institution-${index}`] = el;
                }}
              >
                {edu.institution?.text}
              </span>
              <span
                contentEditable='true'
                className='text-gray-700'
                onInput={(e) => handleEducationChange(e, `year-${index}`)}
                suppressContentEditableWarning={true}
                ref={(el) => {
                  if (!educationRefs.current) educationRefs.current = {};
                  educationRefs.current[`year-${index}`] = el;
                }}
              >
                {" "}
                - {edu.year}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Education;