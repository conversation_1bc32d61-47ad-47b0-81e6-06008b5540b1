import React from "react";
import useCertification from "@/hooks/useCertification";

// Define interfaces for the certification data structure
interface Certification {
  name?: string;
  institution?: string;
  year?: string;
}

// Props interface for the component
interface CertificationsProps {
  certifications: (Certification | string)[];
}

const Certifications: React.FC<CertificationsProps> = ({ certifications: certs }) => {
  const { certifications, handleKeyDown, handleChange, certRefs } =
    useCertification(certs);
    
  return (
    <div className='mb-3 section'>
      <h2 className='text-xl font-bold text-gray-800 mb-1'>Certifications</h2>
      <ul className='list-disc list-inside text-gray-700'>
        {certifications.map((cert, index) => (
          <li
            key={index}
            contentEditable='true'
            onKeyDown={(e: React.KeyboardEvent<HTMLLIElement>) => handleKeyDown(e, index)}
            onInput={(e: React.FormEvent<HTMLLIElement>) => handleChange(e, index)}
            suppressContentEditableWarning={true}
            className='focus:outline-none'
            ref={(el: HTMLLIElement | null) => {
              if (el) certRefs.current[index] = el;
            }} // Store reference for each list item
          >
            {typeof cert === 'object' && cert.name
              ? (`${cert.name} - ${cert.institution} (${cert.year})` as React.ReactNode)
              : (cert as React.ReactNode)}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Certifications;