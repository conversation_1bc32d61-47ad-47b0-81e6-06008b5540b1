import React from "react";
import Skills from "./Skills";
import WorkExperience from "./WorkExperience";
import Certifications from "./Certifications";
import Projects from "../Project";
import Achievements from "../Acheivement";
import { CertificationEntry } from "@/hooks/useCertification";
import { ProjectType } from "@/hooks/useProject";
import { AchievementType } from "@/hooks/useAcheivement";
import Education from "./Education";
import {
  ContactInfo,
  WorkExperience as WorkExperienceItem,
} from "@/components/resumes/resumeTypes";

interface MainWrapperProps {
  name: string;
  title: string;
  contactInfo: ContactInfo;
  skills: string[];
  workExperience: WorkExperienceItem[];
  education: {
    degree?: string;
    institution?: { text?: string; url?: string };
    year?: string;
  }[];
  profile: string;
  certifications: CertificationEntry[];
  projects?: ProjectType[];
  achievements?: AchievementType[];
}

const MainWrapper: React.FC<MainWrapperProps> = ({
  name,
  title,
  contactInfo,
  skills,
  workExperience,
  education,
  profile,
  certifications,
  projects,
  achievements,
}) => {
  return (
    <div className="max-w-4xl mx-auto bg-white mt-4 p-4 rounded-lg resume-container no-padding-margin-top">
      {/* Header */}
      <div className="flex flex-col items-center text-center border-b pb-3 mb-3 resume-container">
        <h1
          contentEditable="true"
          className="text-4xl font-bold text-indigo-700"
          suppressContentEditableWarning={true}
        >
          {name}
        </h1>
        <p
          contentEditable="true"
          className="text-lg text-gray-500"
          suppressContentEditableWarning={true}
        >
          {title}
        </p>
        <div className="flex mt-2 space-x-6 text-gray-700">
          <p contentEditable="true" suppressContentEditableWarning={true}>
            <strong>Email:</strong> {contactInfo.email}
          </p>
          <p contentEditable="true" suppressContentEditableWarning={true}>
            <strong>Phone:</strong> {contactInfo.phone}
          </p>
          <p contentEditable="true" suppressContentEditableWarning={true}>
            <strong>Location:</strong> {contactInfo.location}
          </p>
        </div>
      </div>

      {/* Main Content: Two-Column Layout */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 add-margin-top">
        {/* Left Column: Skills and Profile */}
        <div className="col-span-1">
          <div className="mb-3">
            <h2 className="text-xl font-bold text-gray-800 mb-1">Profile</h2>
            <p
              contentEditable="true"
              className="text-gray-700"
              suppressContentEditableWarning={true}
            >
              {profile}
            </p>
          </div>
          {skills.length > 0 && <Skills skills={skills} />}
          <Education education={education} />
        </div>

        {/* Right Column: Experience, Projects, Achievements, and Certifications */}
        <div className="col-span-2">
          {workExperience && workExperience.length > 0 && <WorkExperience experience={workExperience} />}
          {projects && projects.length > 0 && (
            <Projects projects={projects} />
          )}
          {achievements && achievements.length > 0 && (
            <Achievements achievements={achievements} />
          )}
          {certifications.length > 0 && (
            <Certifications certifications={certifications} />
          )}
        </div>
      </div>
    </div>
  );
};

export default MainWrapper;
