import React, { KeyboardEvent, FormEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus } from "lucide-react";
import useProject, { ProjectType } from "@/hooks/useProject";

interface ProjectsProps {
  projects: ProjectType[];
}

const Projects: React.FC<ProjectsProps> = ({ projects }) => {
  const {
    projects: editedProjects,
    handleTechKeyDown,
    handleTechChange,
    handleProjectFieldChange,
    handleAddProject,
    handleRemoveProject,
    techRefs,
    projectRefs,
  } = useProject(projects);

  return (
    <section className="mb-3">
      {/* Header */}
      <div className="flex justify-between mb-2">
        <h2 className="text-xl font-bold text-gray-800">Projects</h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleAddProject}
          aria-label="Add project"
          className="button"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Projects List */}
      {editedProjects.map((project, projectIndex) => (
        <div key={projectIndex} className="mb-2 section">
          <div className="flex justify-between items-start gap-2">
            <div className="w-full space-y-1">
              {/* Project Name */}
              <h3
                contentEditable
                suppressContentEditableWarning
                onInput={(e: FormEvent<HTMLElement>) =>
                  handleProjectFieldChange(e, projectIndex, 'name.text')
                }
                ref={(el) => {
                  projectRefs.current[`name-${projectIndex}`] = el;
                }}
                className="text-md font-semibold text-gray-900 focus:outline-none"
              >
                {project.name.url ? (
                  <a 
                    href={project.name.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {project.name.text}
                  </a>
                ) : (
                  project.name.text
                )}
              </h3>

              {/* Project URL (editable) */}
              {project.name.url && (
                <p className="text-xs text-gray-500">
                  URL: 
                  <span
                    contentEditable
                    suppressContentEditableWarning
                    onInput={(e: FormEvent<HTMLElement>) =>
                      handleProjectFieldChange(e, projectIndex, 'name.url')
                    }
                    ref={(el) => {
                      projectRefs.current[`url-${projectIndex}`] = el;
                    }}
                    className="ml-1 focus:outline-none"
                  >
                    {project.name.url}
                  </span>
                </p>
              )}

              {/* Project Dates */}
              <p
                contentEditable
                suppressContentEditableWarning
                onInput={(e: FormEvent<HTMLElement>) =>
                  handleProjectFieldChange(e, projectIndex, 'dates')
                }
                ref={(el) => {
                  projectRefs.current[`dates-${projectIndex}`] = el;
                }}
                className="text-gray-500 focus:outline-none"
              >
                {project.dates}
              </p>

              {/* Project Description */}
              <p
                contentEditable
                suppressContentEditableWarning
                onInput={(e: FormEvent<HTMLElement>) =>
                  handleProjectFieldChange(e, projectIndex, 'description')
                }
                ref={(el) => {
                  projectRefs.current[`description-${projectIndex}`] = el;
                }}
                className="text-gray-700 focus:outline-none"
              >
                {project.description}
              </p>

              {/* Technologies */}
              <div>
                <strong className="text-gray-800">Technologies:</strong>
                <ul className="list-disc list-inside text-gray-700 pl-4 mt-1">
                  {project.technologies.map((tech, techIndex) => (
                    <li
                      key={techIndex}
                      contentEditable
                      suppressContentEditableWarning
                      onKeyDown={(e: KeyboardEvent<HTMLLIElement>) =>
                        handleTechKeyDown(e, projectIndex, techIndex)
                      }
                      onInput={(e: FormEvent<HTMLLIElement>) =>
                        handleTechChange(e, projectIndex, techIndex)
                      }
                      ref={(el: HTMLLIElement | null) => {
                        if (!el) return;
                        if (!techRefs.current[projectIndex])
                          techRefs.current[projectIndex] = [];
                        techRefs.current[projectIndex][techIndex] = el;
                      }}
                      className="focus:outline-none"
                    >
                      {tech}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Remove Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleRemoveProject(projectIndex)}
              aria-label="Remove project"
              className="mt-1 button"
            >
              <Minus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </section>
  );
};

export default Projects;