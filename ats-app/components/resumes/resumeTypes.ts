export interface ContactInfo {
  email?: string;
  phone?: string;
  linkedin?: string;
  website?: string;
  location?: string;
  [key: string]: string | string[] | undefined;
}

export interface WorkExperience {
  company: { text?: string; url?: string };
  title: string;
  location: string;
  period: string;
  tasks: string[];
}

export interface Education {
  degree?: string;
  institution?: string;
  year?: string;
  [key: string]: string | undefined;
}

export interface ResumeData {
  name: string;
  title: string;
  professional_summary?: string;
  contact_info: ContactInfo;
  work_experience: WorkExperience[];
  education: Education[];
  skills?: string[];
  certifications?: string[];
  [key: string]:
    | string
    | string[]
    | ContactInfo
    | WorkExperience[]
    | Education[]
    | undefined;
}

export interface Template {
  id: number;
  name: string;
  image: string;
}

export interface ToasterData {
  message: string;
  severity: "success" | "error" | "warning" | "info";
  open: boolean;
}

export interface GenerateResumeProps {
  params: Promise<{
    id: string;
  }>;
}