import React, { useRef } from "react";

type EducationProps = {
  education: {
    degree?: string;
    institution?: { text?: string; url?: string };
    year?: string;
  }[];
};

type EducationField = "degree" | "institution" | "year";

const Education: React.FC<EducationProps> = ({ education }) => {
  const educationRefs = useRef<
    Array<Partial<Record<EducationField, HTMLDivElement>>>
  >([]);

  const handleEducationChange = (
    e: React.FormEvent<HTMLDivElement>,
    field: EducationField,
    index: number
  ) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const cursorPos = range.endOffset;

    // Re-set the cursor position after the render
    setTimeout(() => {
      const el = educationRefs.current[index]?.[field];
      if (el && el.firstChild) {
        const newRange = document.createRange();
        newRange.setStart(
          el.firstChild,
          Math.min(cursorPos, el.firstChild.textContent?.length || 0)
        );
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    }, 0);
  };

  return (
    <div className="mb-3 section">
      <div className="flex justify-between items-center mb-1">
        <h2 className="text-xl font-bold text-gray-800">Education</h2>
      </div>
      {education.map((edu, index) => (
        <div className="mb-2" key={index}>
          <div className="flex justify-between items-center">
            <div>
              <div
                contentEditable
                className="text-lg font-semibold text-gray-900"
                onInput={(e) => handleEducationChange(e, "degree", index)}
                suppressContentEditableWarning
                ref={(el) => {
                  if (!educationRefs.current[index]) {
                    educationRefs.current[index] = {};
                  }
                  if (el) educationRefs.current[index].degree = el;
                }}
              >
                {edu.degree}
              </div>
              <div
                contentEditable
                className="text-gray-600"
                onInput={(e) => handleEducationChange(e, "institution", index)}
                suppressContentEditableWarning
                ref={(el) => {
                  if (!educationRefs.current[index]) {
                    educationRefs.current[index] = {};
                  }
                  if (el) educationRefs.current[index].institution = el;
                }}
              >
                {edu.institution?.text}
              </div>
              <div
                contentEditable
                className="text-gray-500"
                onInput={(e) => handleEducationChange(e, "year", index)}
                suppressContentEditableWarning
                ref={(el) => {
                  if (!educationRefs.current[index]) {
                    educationRefs.current[index] = {};
                  }
                  if (el) educationRefs.current[index].year = el;
                }}
              >
                {edu.year}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Education;
