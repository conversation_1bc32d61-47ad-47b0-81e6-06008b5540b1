import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus } from "lucide-react";
import useWorkExperience from "@/hooks/useWorkExperience";
import { WorkExperience as WorkExperienceItem } from "@/components/resumes/resumeTypes";

interface WorkExperienceProps {
  experience: WorkExperienceItem[];
}

const WorkExperience: React.FC<WorkExperienceProps> = ({ experience }) => {
  const {
    workExperience,
    handleTaskKeyDown,
    handleTaskChange,
    handleAddWorkExperience,
    handleRemoveWorkExperience,
    taskRefs,
  } = useWorkExperience(experience);

  return (
    <div className="mb-3">
      <div className="flex justify-between mb-2">
        <h2 className="text-xl font-bold text-gray-800">Work Experience</h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleAddWorkExperience}
          aria-label="add work experience"
          className="button"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      {workExperience?.map((job, jobIndex) => (
        <div key={jobIndex} className="mb-2 section">
          <div className="flex justify-between items-start">
            <div className="w-full">
              <h3
                contentEditable={true}
                className="text-md font-semibold text-gray-900"
                suppressContentEditableWarning={true}
              >
                {job.title}
              </h3>
              <p
                contentEditable={true}
                className="text-gray-600"
                suppressContentEditableWarning={true}
              >
                {job.company.text}, {job.location}
              </p>
              <p
                contentEditable={true}
                className="text-gray-500"
                suppressContentEditableWarning={true}
              >
                {job.period}
              </p>
              <ul className="list-disc list-inside text-gray-700 pl-1">
                {job.tasks?.map((task: string, taskIndex: number) => (
                  <li
                    key={taskIndex}
                    contentEditable={true}
                    onKeyDown={(e: React.KeyboardEvent<HTMLLIElement>) =>
                      handleTaskKeyDown(e, jobIndex, taskIndex)
                    }
                    onInput={(e: React.FormEvent<HTMLLIElement>) =>
                      handleTaskChange(e, jobIndex, taskIndex)
                    }
                    suppressContentEditableWarning={true}
                    className="focus:outline-none"
                    ref={(el: HTMLLIElement | null) => {
                      if (!el) return;
                      if (!taskRefs.current[jobIndex])
                        taskRefs.current[jobIndex] = [];
                      taskRefs.current[jobIndex][taskIndex] = el;
                    }}
                  >
                    {task}
                  </li>
                ))}
              </ul>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleRemoveWorkExperience(jobIndex)}
              aria-label="remove work experience"
              className="mt-1 button"
            >
              <Minus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default WorkExperience;
