import React from "react";
import KeySkills from "./KeySkills";
import WorkExperience from "./WorkExperience";
import Education from "./Education";
import Projects from "../Project";
import Achievements from "../Acheivement";
import { WorkExperience as WorkExperienceType } from "@/components/resumes/resumeTypes";
import { ProjectType } from "@/hooks/useProject";
import { AchievementType } from "@/hooks/useAcheivement";

interface ContactInfo {
  email?: string;
  phone?: string;
  linkedin?: string;
  website?: string;
  location?: string;
  [key: string]: string | string[] | undefined;
}

interface EducationInfo {
  degree?: string;
  institution?: { text?: string; url?: string };
  year?: string;
}

interface MainWrapperProps {
  name: string;
  title: string;
  contactInfo: ContactInfo;
  skills: string[];
  workExperience: WorkExperienceType[];
  education: EducationInfo[];
  profile?: string;
  projects?: ProjectType[];
  achievements?: AchievementType[];
}

const MainWrapper: React.FC<MainWrapperProps> = ({
  name,
  title,
  contactInfo,
  skills,
  workExperience,
  education,
  profile,
  projects,
  achievements,
}) => {
  return (
    <div className="max-w-4xl mx-auto bg-white p-4 mt-4 resume-container no-padding-margin-top">
      {/* Header */}
      <div className="text-center mb-1">
        <h1
          contentEditable={true}
          suppressContentEditableWarning={true}
          className="text-4xl font-bold text-gray-900"
        >
          {name}
        </h1>
        <p
          contentEditable={true}
          suppressContentEditableWarning={true}
          className="text-lg text-gray-600"
        >
          {title}
        </p>
      </div>
      {/* Contact Information */}
      <div className="flex justify-center space-x-6 mb-3">
        <div>
          <p className="text-gray-700">
            <strong>Email:</strong>{" "}
            <span contentEditable={true} suppressContentEditableWarning={true}>
              {contactInfo?.email || ""}
            </span>
          </p>
        </div>
        <div>
          <p className="text-gray-700">
            <strong>Phone:</strong>{" "}
            <span contentEditable={true} suppressContentEditableWarning={true}>
              {contactInfo?.phone ?? ""}
            </span>
          </p>
        </div>
        <div>
          <p className="text-gray-700">
            <strong>Location:</strong>{" "}
            <span contentEditable={true} suppressContentEditableWarning={true}>
              {contactInfo?.location || ""}
            </span>
          </p>
        </div>
      </div>
      {/* Professional Summary */}
      <div className="mb-3 section">
        <h2 className="text-xl font-bold text-gray-800 mb-1">
          Professional Summary
        </h2>
        <p
          contentEditable={true}
          suppressContentEditableWarning={true}
          className="text-gray-700"
        >
          {profile}
        </p>
      </div>
      {skills?.length > 0 && <KeySkills skills={skills} />}
      {workExperience?.length > 0 && (
        <WorkExperience experience={workExperience} />
      )}
      {projects && projects.length > 0 && (
        <Projects projects={projects} />
      )}
      {achievements && achievements.length > 0 && (
        <Achievements achievements={achievements} />
      )}
      {education && <Education education={education} />}
    </div>
  );
};

export default MainWrapper;
