import React from "react";
import useKeySkill from "@/hooks/useKeySkill";

type KeySkillsProps = {
  skills: string[];
};

const KeySkills: React.FC<KeySkillsProps> = ({ skills }) => {
  const {
    skills: editedSkills,
    handleKeyDown,
    handleChange,
    skillRefs,
  } = useKeySkill(skills);

  return (
    <div className="mb-3 section">
      <h2 className="text-xl font-bold text-gray-800 mb-1">Skills</h2>
      <ul className="list-disc list-inside text-gray-700">
        {editedSkills?.map((skill: string, index: number) => (
          <li
            key={index}
            id={`skill-${index}`}
            contentEditable
            onKeyDown={(e: React.KeyboardEvent<HTMLLIElement>) =>
              handleKeyDown(e, index)
            }
            onInput={(e: React.FormEvent<HTMLLIElement>) =>
              handleChange(e, index)
            }
            suppressContentEditableWarning
            className="focus:outline-none"
            ref={(el) => {
              if (el) skillRefs.current[index] = el;
            }}
          >
            {skill}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default KeySkills;

