import React, { KeyboardEvent, FormEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus } from "lucide-react";
import useAcheivement, { AchievementType } from "@/hooks/useAcheivement";

interface AchievementsProps {
  achievements: AchievementType[];
}

const Achievements: React.FC<AchievementsProps> = ({ achievements }) => {
  const {
    achievements: editedAchievements,
    handleTaskKeyDown,
    handleTaskChange,
    handleAchievementFieldChange,
    handleAddAchievement,
    handleRemoveAchievement,
    taskRefs,
    achievementRefs,
  } = useAcheivement(achievements);

  return (
    <section className="mb-3">
      {/* Header */}
      <div className="flex justify-between mb-2">
        <h2 className="text-xl font-bold text-gray-800">Achievements</h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleAddAchievement}
          aria-label="Add achievement"
          className="button"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Achievements List */}
      {editedAchievements.map((achievement, achievementIndex) => (
        <div key={achievementIndex} className="mb-2 section">
          <div className="flex justify-between items-start gap-2">
            <div className="w-full space-y-1">
              {/* Achievement Title */}
              <h3
                contentEditable
                suppressContentEditableWarning
                onInput={(e: FormEvent<HTMLElement>) =>
                  handleAchievementFieldChange(e, achievementIndex, 'title')
                }
                ref={(el) => {
                  achievementRefs.current[`title-${achievementIndex}`] = el;
                }}
                className="text-md font-semibold text-gray-900 focus:outline-none"
              >
                {achievement.title}
              </h3>

              {/* Achievement Date */}
              {achievement.date && (
                <p
                  contentEditable
                  suppressContentEditableWarning
                  onInput={(e: FormEvent<HTMLElement>) =>
                    handleAchievementFieldChange(e, achievementIndex, 'date')
                  }
                  ref={(el) => {
                    achievementRefs.current[`date-${achievementIndex}`] = el;
                  }}
                  className="text-gray-500 focus:outline-none"
                >
                  {achievement.date}
                </p>
              )}

              {/* Achievement Tasks */}
              <ul className="list-disc list-inside text-gray-700 pl-1 space-y-1">
                {achievement.tasks.map((task, taskIndex) => (
                  <li
                    key={taskIndex}
                    contentEditable
                    suppressContentEditableWarning
                    onKeyDown={(e: KeyboardEvent<HTMLLIElement>) =>
                      handleTaskKeyDown(e, achievementIndex, taskIndex)
                    }
                    onInput={(e: FormEvent<HTMLLIElement>) =>
                      handleTaskChange(e, achievementIndex, taskIndex)
                    }
                    ref={(el: HTMLLIElement | null) => {
                      if (!el) return;
                      if (!taskRefs.current[achievementIndex])
                        taskRefs.current[achievementIndex] = [];
                      taskRefs.current[achievementIndex][taskIndex] = el;
                    }}
                    className="focus:outline-none"
                  >
                    {task}
                  </li>
                ))}
              </ul>
            </div>

            {/* Remove Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleRemoveAchievement(achievementIndex)}
              aria-label="Remove achievement"
              className="mt-1 button"
            >
              <Minus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </section>
  );
};

export default Achievements;