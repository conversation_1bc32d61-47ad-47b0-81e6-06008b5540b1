import React, { useRef } from "react";

interface EducationProps {
  education: {
    degree?: string;
    institution?: { text?: string; url?: string };
    year?: string;
  }[];
}

interface EducationRefs {
  [key: string]: HTMLParagraphElement | null;
}

const Education: React.FC<EducationProps> = ({ education }) => {
  const educationRefs = useRef<EducationRefs>({}); // Store refs for each education field

  const handleEducationChange = (
    e: React.FormEvent<HTMLParagraphElement>,
    field: string
  ): void => {
    // Store the current cursor position
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const cursorPos = range.endOffset;

    // Set the cursor position back after re-rendering
    setTimeout(() => {
      const el = educationRefs.current[field];
      if (!el || !el.childNodes.length) return;

      const newRange = document.createRange();
      newRange.setStart(
        el.childNodes[0],
        Math.min(cursorPos, (el.childNodes[0] as Text).length)
      );
      newRange.collapse(true);
      selection.removeAllRanges();
      selection.addRange(newRange);
    }, 0);
  };

  return (
    <div className="mb-3 section">
      <h2 className="text-xl font-bold text-gray-800 mb-1">Education</h2>
      <div className="text-gray-700">
        {education.map((edu, index) => (
          <div key={index}>
            <p
              contentEditable={true}
              onInput={(e) => handleEducationChange(e, `degree-${index}`)}
              suppressContentEditableWarning={true}
              ref={(el) => {
                educationRefs.current[`degree-${index}`] = el;
              }}
            >
              <strong>{edu.degree}</strong>
            </p>
            <p
              contentEditable={true}
              onInput={(e) => handleEducationChange(e, `institution-${index}`)}
              suppressContentEditableWarning={true}
              ref={(el) => {
                educationRefs.current[`institution-${index}`] = el;
              }}
            >
              {edu.institution?.text}
            </p>
            <p
              contentEditable={true}
              onInput={(e) => handleEducationChange(e, `year-${index}`)}
              suppressContentEditableWarning={true}
              ref={(el) => {
                educationRefs.current[`year-${index}`] = el;
              }}
            >
              {edu.year}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Education;
