import React, { useState, useEffect } from "react";
import Skills from "./KeySkills";
import Education from "./Education";
import WorkExperience from "./WorkExperience";
import Certifications from "./Certification";
import Projects from "../Project";
import Achievements from "../Acheivement";
import { CertificationEntry } from "@/hooks/useCertification";
import { ContactInfo , WorkExperience as WorkExperienceItem } from "@/components/resumes/resumeTypes";
import { ProjectType } from "@/hooks/useProject";
import { AchievementType } from "@/hooks/useAcheivement";

interface MainWrapperProps {
  name: string;
  title: string;
  contactInfo: ContactInfo;
  skills: string[];
  workExperience?: WorkExperienceItem[];
  education: {
    degree?: string;
    institution?: { text?: string; url?: string };
    year?: string;
  }[];
  profile: string;
  certifications?: CertificationEntry[];
  projects?: ProjectType[];
  achievements?: AchievementType[];
}

const MainWrapper: React.FC<MainWrapperProps> = ({
  name: initialName,
  title: initialTitle,
  contactInfo: initialContactInfo,
  skills,
  workExperience,
  education,
  profile: initialProfile,
  certifications,
  projects,
  achievements,
}) => {
  // State for editable content
  const [editableContent, setEditableContent] = useState({
    name: initialName,
    title: initialTitle,
    email: initialContactInfo.email,
    phone: initialContactInfo.phone,
    location: initialContactInfo.location,
    profile: initialProfile,
  });

  // Update state when props change
  useEffect(() => {
    setEditableContent({
      name: initialName,
      title: initialTitle,
      email: initialContactInfo.email,
      phone: initialContactInfo.phone,
      location: initialContactInfo.location,
      profile: initialProfile,
    });
  }, [initialName, initialTitle, initialContactInfo, initialProfile]);

  // Handlers for contentEditable elements
  const handleContentChange = (field: keyof typeof editableContent, value: string) => {
    setEditableContent((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // ContentEditable component to safely handle editable content
  const EditableContent = ({ 
    value, 
    onChange, 
    className = "", 
    as = "div" 
  }: {
    value: string;
    onChange: (value: string) => void;
    className?: string;
    as?: string;
  }) => {
    // Handle content changes
    const handleInput = (e: React.FormEvent<HTMLElement>) => {
      const newValue = e.currentTarget.textContent || "";
      onChange(newValue);
    };

    const props = {
      className,
      contentEditable: true,
      suppressContentEditableWarning: true,
      onInput: handleInput,
      dangerouslySetInnerHTML: { __html: value }
    };

    switch (as) {
      case "h1":
        return <h1 {...props} />;
      case "h2":
        return <h2 {...props} />;
      case "h3":
        return <h3 {...props} />;
      case "p":
        return <p {...props} />;
      case "span":
        return <span {...props} />;
      default:
        return <div {...props} />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white mt-10 py-3 px-6 resume-container no-padding-margin-top">
      {/* Header */}
      <div className="flex flex-col md:flex-col justify-between items-center border-b pb-3 mb-3 resume-container">
        <div className="text-center mb-1">
          <EditableContent
            as="h1"
            className="text-3xl font-bold text-gray-900"
            value={editableContent.name}
            onChange={(value) => handleContentChange("name", value)}
          />
          <EditableContent
            as="p"
            className="text-lg text-gray-600"
            value={editableContent.title}
            onChange={(value) => handleContentChange("title", value)}
          />
        </div>
        <div className="flex space-x-6 text-gray-700 mt-2 md:mt-0">
          <p>
            <strong>Email:</strong>{" "}
            <EditableContent
              as="span"
              value={editableContent.email || ""}
              onChange={(value) => handleContentChange("email", value)}
            />
          </p>
          <p>
            <strong>Phone:</strong>{" "}
            <EditableContent
              as="span"
              value={editableContent.phone || ""}
              onChange={(value) => handleContentChange("phone", value)}
            />
          </p>
          <p>
            <strong>Location:</strong>{" "}
            <EditableContent
              as="span"
              value={editableContent.location || ""}
              onChange={(value) => handleContentChange("location", value)}
            />
          </p>
        </div>
      </div>
      {/* Main Content: Two-Column Layout */}
      <div className="flex flex-col md:flex-row add-margin-top">
        {/* Left Column: Skills and Profile */}
        <div className="w-full md:w-1/3 pr-3">
          {/* Profile Summary */}
          <div className="mb-3">
            <h2 className="text-xl font-bold text-gray-800 mb-1">Profile</h2>
            <EditableContent
              as="p"
              className="text-gray-700"
              value={editableContent.profile}
              onChange={(value) => handleContentChange("profile", value)}
            />
          </div>
          <Skills skills={skills} />
          <Education education={education} />
        </div>
        {/* Right Column: Experience, Projects, Achievements, and Certifications */}
        <div className="w-full md:w-2/3">
          {workExperience && workExperience.length > 0 && <WorkExperience experience={workExperience} />}
          {projects && projects.length > 0 && (
            <Projects projects={projects} />
          )}
          {achievements && achievements.length > 0 && (
            <Achievements achievements={achievements} />
          )}
          {certifications && certifications.length > 0 && (
            <Certifications certifications={certifications} />
          )}
        </div>
      </div>
    </div>
  );
};

export default MainWrapper;