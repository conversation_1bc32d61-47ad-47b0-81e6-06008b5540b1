import useCertification, { CertificationEntry } from "@/hooks/useCertification";
import React from "react";

interface CertificationsProps {
  certifications: CertificationEntry[];
}

const Certifications: React.FC<CertificationsProps> = ({ certifications: certs }) => {
  const { certifications, handleKeyDown, handleChange, certRefs } =
    useCertification(certs);

  return (
    <div className="mb-3 section">
      <h2 className="text-xl font-bold text-gray-800 mb-1">Certifications</h2>
      <ul className="list-disc list-inside text-gray-700">
        {certifications.map((cert, index) => (
          <li
            key={index}
            contentEditable={true}
            onKeyDown={(e) => handleKeyDown(e, index)}
            onInput={(e) => handleChange(e as React.FormEvent<HTMLLIElement>, index)}
            suppressContentEditableWarning={true}
            className="focus:outline-none"
            ref={(el: HTMLLIElement | null) => {
              if (el) certRefs.current[index] = el;
            }}
          >
            {typeof cert === 'object' && cert.name
              ? `${cert.name} - ${cert.institution || ''} ${cert.year ? `(${cert.year})` : ''}`
              : String(cert)}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Certifications;