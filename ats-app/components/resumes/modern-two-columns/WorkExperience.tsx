import React, { KeyboardEvent, FormEvent } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Minus } from "lucide-react";
import useWorkExperience from "@/hooks/useWorkExperience";
import { WorkExperience as WorkExperienceItem } from "@/components/resumes/resumeTypes";

interface WorkExperienceProps {
  experience: WorkExperienceItem[];
}

const WorkExperience: React.FC<WorkExperienceProps> = ({ experience }) => {
  const {
    workExperience,
    handleTaskKeyDown,
    handleTaskChange,
    handleAddWorkExperience,
    handleRemoveWorkExperience,
    taskRefs,
  } = useWorkExperience(experience);

  return (
    <section className="mb-3">
      {/* Header */}
      <div className="flex justify-between mb-2">
        <h2 className="text-xl font-bold text-gray-800">Work Experience</h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleAddWorkExperience}
          aria-label="Add work experience"
          className="button"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Experience List */}
      {workExperience.map((job, jobIndex) => (
        <div key={jobIndex} className="mb-2 section">
          <div className="flex justify-between items-start gap-2">
            <div className="w-full space-y-1">
              <h3
                contentEditable
                suppressContentEditableWarning
                className="text-md font-semibold text-gray-900"
              >
                {job.title}
              </h3>
              <p
                contentEditable
                suppressContentEditableWarning
                className="text-gray-600"
              >
                {job.company.text}, {job.location}
              </p>
              <p
                contentEditable
                suppressContentEditableWarning
                className="text-gray-500"
              >
                {job.period}
              </p>

              {/* Task List */}
              <ul className="list-disc list-inside text-gray-700 pl-1 space-y-1">
                {(job.tasks ?? []).map((task: string, taskIndex:number) => (
                  <li
                    key={taskIndex}
                    contentEditable
                    suppressContentEditableWarning
                    onKeyDown={(e: KeyboardEvent<HTMLLIElement>) =>
                      handleTaskKeyDown(e, jobIndex, taskIndex)
                    }
                    onInput={(e: FormEvent<HTMLLIElement>) =>
                      handleTaskChange(e, jobIndex, taskIndex)
                    }
                    ref={(el: HTMLLIElement | null) => {
                      if (!el) return;
                      if (!taskRefs.current[jobIndex])
                        taskRefs.current[jobIndex] = [];
                      taskRefs.current[jobIndex][taskIndex] = el;
                    }}
                    className="focus:outline-none"
                  >
                    {task}
                  </li>
                ))}
              </ul>
            </div>

            {/* Remove Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleRemoveWorkExperience(jobIndex)}
              aria-label="Remove work experience"
              className="mt-1 button"
            >
              <Minus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </section>
  );
};

export default WorkExperience;
