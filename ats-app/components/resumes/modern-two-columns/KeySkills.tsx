import useKeySkill from "@/hooks/useKeySkill";
import React from "react";

interface SkillsProps {
  skills: string[];
}

const Skills: React.FC<SkillsProps> = ({ skills }) => {
  const {
    skills: editedSkills,
    handleKeyDown,
    handleChange,
    skillRefs,
  } = useKeySkill(skills);

  return (
    <div className="mb-3">
      <h2 className="text-xl font-bold text-gray-800 mb-1">Skills</h2>
      <ul className="list-disc list-inside text-gray-700">
        {editedSkills.map((skill, index) => (
          <li
            key={index}
            contentEditable="true"
            onKeyDown={(e) => handleKeyDown(e, index)}
            onInput={(e) => handleChange(e, index)}
            suppressContentEditableWarning={true}
            className="focus:outline-none"
            ref={(el) => {
              if (el) skillRefs.current[index] = el;
            }} // Store reference for each list item
          >
            {skill}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Skills;
