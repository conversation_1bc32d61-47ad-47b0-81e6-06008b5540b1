'use client';
import { signOut } from "@/app/auth/login/action";
import { But<PERSON> } from "@/components/ui/button";
import { createClient } from "@/utils/supabase/client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { UserIcon, SettingsIcon } from "lucide-react"; // Import icons from lucide-react

import { SidebarTrigger } from "@/components/ui/sidebar"
export default function Header() {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      const supabase = await createClient();
      const { data: { user: userdata } } = await supabase.auth.getUser();
      if (userdata) {
        setUser(userdata);
      }
    };
    fetchUser();
  }, []);

  return (
    <header className="z-10 sticky navbar top-0 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 max-w-screen-2xl items-center justify-between px-6">
        <SidebarTrigger />

        {/* User Section */}
        <div className="flex items-center space-x-4">

          {/* Settings Icon */}
          <Link href="/settings" className="text-lg">
            <SettingsIcon size={24} />
          </Link>

          <span className="border border-foreground h-6" />

          {/* User Icon */}
          <Link href="/profile" className="text-lg">
            <UserIcon size={24} />
          </Link>

          {/* Sign Out Button */}
          {user !== null && (
            <form action={signOut} className="flex items-center gap-2">
              <Button size="sm">Log Out</Button>
            </form>
          ) }
        </div>
      </div>
    </header>
  );
}
