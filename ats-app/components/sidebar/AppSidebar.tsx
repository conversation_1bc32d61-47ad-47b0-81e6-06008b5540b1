import React from "react";
import {
  // Settings,
  Search,
  FileText,
  Files,
  Clock,
  LayoutDashboard,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarHeader,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarFooter,
} from "@/components/ui/sidebar";
import Image from "next/image";

import NavUser from "./nav-user";

const AppSidebar = async () => {

  const items = [
    {
      title: "Dashboard",
      url: "/",
      icon: LayoutDashboard,
    },
    {
      title: "Job Search",
      url: "/job-search",
      icon: Search,
    },
    {
      title: "ATS Score Checker",
      url: "/ats",
      icon: Files,
    },
    {
      title: "Cover Letter Builder",
      url: "/cover-letter",
      icon: FileText,
    },
    {
      title: "History",
      url: "/history",
      icon: Clock,
    },
    // { title: "Settings",
    //   url: "/settings",
    //   icon: Settings,
    // },
  ];
  return (
    <Sidebar variant="floating" collapsible="icon" className="">
      <SidebarHeader className="mt-4 group-data-[collapsible=icon]:hidden">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <div className="flex items-center gap-2">
                <Image 
                  src="/images/speeduphire-logo.png" 
                  alt="SpeedupHire Logo" 
                  width={42} 
                  height={42} 
                />
                <span className="font-bold text-lg text-primary">SpeedupHire</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <span className="h-px m-4 bg-border group-data-[collapsible=icon]:hidden" />
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    <SidebarFooter>
      <NavUser />
    </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;
