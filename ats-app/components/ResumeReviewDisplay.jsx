import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, AlertCircle, XCircle, Clock } from "lucide-react";

export default function ResumeReviewDisplay({ reviewResponse }) {
  const { data = {} } = reviewResponse;

  // Helper function to get status color and icon
  const getStatusInfo = (status, score) => {
    if (typeof status === 'string') {
      switch (status.toLowerCase()) {
        case 'excellent':
          return { color: 'bg-green-100 text-green-800', icon: CheckCircle, textColor: 'text-green-600' };
        case 'good':
          return { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, textColor: 'text-blue-600' };
        case 'needs_improvement':
          return { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle, textColor: 'text-yellow-600' };
        case 'poor':
          return { color: 'bg-red-100 text-red-800', icon: XCircle, textColor: 'text-red-600' };
        default:
          return { color: 'bg-gray-100 text-gray-800', icon: Clock, textColor: 'text-gray-600' };
      }
    }

    // Fallback based on score if status is not provided
    if (score >= 90) return { color: 'bg-green-100 text-green-800', icon: CheckCircle, textColor: 'text-green-600' };
    if (score >= 75) return { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, textColor: 'text-blue-600' };
    if (score >= 60) return { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle, textColor: 'text-yellow-600' };
    return { color: 'bg-red-100 text-red-800', icon: XCircle, textColor: 'text-red-600' };
  };

  // Helper function to get priority color
  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // List of categories to display, ordered for presentation
  const categories = [
    { key: "formatting_and_layout", label: "Formatting & Layout" },
    { key: "header_and_contact_info", label: "Header & Contact Info" },
    { key: "skills_summary", label: "Skills Summary" },
    { key: "work_experience", label: "Work Experience" },
    { key: "education", label: "Education" },
    { key: "certifications", label: "Certifications" },
    { key: "projects", label: "Projects" },
    { key: "achievements", label: "Achievements" },
    { key: "language_grammar_tone", label: "Language, Grammar & Tone" },
    { key: "ats_compatibility", label: "ATS Compatibility" },
  ];

  // Check if we have the new enhanced format
  const isEnhancedFormat = data.overall_score !== undefined;

  if (!isEnhancedFormat) {
    // Fallback to old format display
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-primary mb-4">{data.title}</h2>
        </div>

        <div className="space-y-6">
          {categories.map((category) => {
            const content = data[category.key];
            if (!content) return null;

            // Handle both string and object content for backward compatibility
            const displayContent = typeof content === 'string' ? content : content.feedback || 'No feedback available';

            return (
              <div key={category.key} className="border-b pb-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {category.label}
                </h3>
                <p className="text-gray-600">{displayContent}</p>
              </div>
            );
          })}

          {data.overall_summary && (
            <div className="border-b pb-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Overall Summary</h3>
              <p className="text-gray-600">{typeof data.overall_summary === 'string' ? data.overall_summary : 'Summary not available'}</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>{data.title}</span>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-3xl font-bold text-primary">{data.overall_score}/100</div>
                <div className="text-sm text-gray-500">Overall Score</div>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {data.experience_level && (
              <div>
                <span className="text-sm font-medium text-gray-500">Experience Level</span>
                <Badge variant="outline" className="ml-2 capitalize">
                  {data.experience_level.replace('_', ' ')}
                </Badge>
              </div>
            )}
            {data.resume_type && (
              <div>
                <span className="text-sm font-medium text-gray-500">Resume Type</span>
                <Badge variant="outline" className="ml-2 capitalize">
                  {data.resume_type}
                </Badge>
              </div>
            )}
          </div>
          <Progress value={data.overall_score} className="w-full" />
        </CardContent>
      </Card>

      {/* Weightage Breakdown */}
      {data.section_weights && data.section_weights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="w-5 h-5 mr-2">⚖️</span>
              Scoring Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.weightage_reasoning && (
                <p className="text-sm text-gray-600 mb-4 p-3 bg-blue-50 rounded-md">
                  <strong>Why these weights:</strong> {data.weightage_reasoning}
                </p>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {data.section_weights.map((weight, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-900 capitalize">
                        {weight.section.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-bold text-gray-900">{weight.weight}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top Priorities */}
      {data.top_priorities && data.top_priorities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2 text-red-500" />
              Top Priorities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {data.top_priorities.map((priority, index) => (
                <li key={index} className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{priority}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Section-wise Reviews */}
      <div className="grid gap-6">
        {categories.map((category) => {
          const sectionData = data[category.key];
          if (!sectionData) return null;

          // Skip if it's a string (old format) since we're in enhanced format mode
          if (typeof sectionData === 'string') return null;

          // Ensure we have the required properties for enhanced format
          if (!sectionData.score && !sectionData.status && !sectionData.feedback) return null;

          const statusInfo = getStatusInfo(sectionData.status, sectionData.score);
          const StatusIcon = statusInfo.icon;

          return (
            <Card key={category.key}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <StatusIcon className={`w-5 h-5 mr-2 ${statusInfo.textColor}`} />
                    {category.label}
                  </div>
                  <div className="flex items-center space-x-2">
                    {sectionData.priority && (
                      <Badge className={getPriorityColor(sectionData.priority)}>
                        {sectionData.priority} Priority
                      </Badge>
                    )}
                    <Badge className={statusInfo.color}>
                      {sectionData.score}/100
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Progress Bar with Score */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">Score</span>
                    <span className="text-sm font-bold text-gray-900">{sectionData.score || 0}/100</span>
                  </div>
                  <Progress value={sectionData.score || 0} className="w-full" />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Poor (0-59)</span>
                    <span>Needs Work (60-74)</span>
                    <span>Good (75-89)</span>
                    <span>Excellent (90-100)</span>
                  </div>
                </div>

                {/* Status Badge */}
                {sectionData.status && (
                  <div>
                    <Badge className={getStatusInfo(sectionData.status, sectionData.score).color}>
                      {sectionData.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                )}

                {/* Feedback */}
                {sectionData.feedback && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                      Detailed Feedback
                    </h4>
                    <p className="text-gray-600 leading-relaxed">{sectionData.feedback}</p>
                  </div>
                )}

                {/* Improvements */}
                {sectionData.improvements && sectionData.improvements.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                      How to Improve
                    </h4>
                    <div className="space-y-2">
                      {sectionData.improvements.map((improvement, index) => (
                        <div key={index} className="flex items-start bg-orange-50 p-3 rounded-md">
                          <span className="flex-shrink-0 w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                            {index + 1}
                          </span>
                          <span className="text-gray-700 leading-relaxed">{improvement}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Examples */}
                {sectionData.examples && sectionData.examples.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Examples
                    </h4>
                    <div className="bg-green-50 border border-green-200 rounded-md p-4">
                      <div className="space-y-3">
                        {sectionData.examples.map((example, index) => (
                          <div key={index} className="bg-white p-3 rounded border border-green-100">
                            <div className="flex items-start">
                              <span className="flex-shrink-0 w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium mr-2 mt-0.5">
                                ✓
                              </span>
                              <span className="text-sm text-gray-700 italic leading-relaxed">"{example}"</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Wins and Long-term Goals */}
      <div className="grid md:grid-cols-2 gap-6">
        {data.quick_wins && data.quick_wins.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
                Quick Wins
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {data.quick_wins.map((win, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></span>
                    <span className="text-gray-600">{win}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {data.long_term_goals && data.long_term_goals.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="w-5 h-5 mr-2 text-blue-500" />
                Long-term Goals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {data.long_term_goals.map((goal, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></span>
                    <span className="text-gray-600">{goal}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Strengths and Overall Summary */}
      {(data.strengths || data.overall_summary) && (
        <Card>
          <CardHeader>
            <CardTitle>Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {data.strengths && data.strengths.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Key Strengths</h4>
                <ul className="space-y-1">
                  {data.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="flex-shrink-0 w-4 h-4 text-green-500 mt-0.5 mr-2" />
                      <span className="text-gray-600">{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {data.overall_summary && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Overall Assessment</h4>
                <p className="text-gray-600">{data.overall_summary}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}