"use client";

import { useState, useEffect, useRef } from "react";
import { useReactToPrint } from "react-to-print";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Download, RefreshCw } from "lucide-react";
import BasicMainWrapper from "@/components/resumes/classic-professional/MainWrapper";
import ModernMainWrapper from "@/components/resumes/modern-two-columns/MainWrapper";
import CreativeCardLayoutMainWrapper from "@/components/resumes/creative-card-layout/MainWrapper";
import Image from "next/image";


export default function TailoredResumeOverlay({
  isOpen,
  onClose,
  tailoredResumeData,
  improvementSummary,
}) {
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const printRef = useRef();

  // Template options
  const templates = [
    {
      id: 1,
      name: "Classic Professional",
      image: "/images/resume-thumbnails/classic-professional.png",
    },
    {
      id: 2,
      name: "Modern Two-Column",
      image: "/images/resume-thumbnails/modern-two-column.png",
    },
    {
      id: 3,
      name: "Creative Card Layout",
      image: "/images/resume-thumbnails/creative-card-layout.png",
    },
  ];

  // Set default template when overlay opens
  useEffect(() => {
    if (isOpen && !selectedTemplate) {
      setSelectedTemplate(templates[0]);
    }
  }, [isOpen]);

  // Handle ESC key to close overlay
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
      // Prevent body scroll when overlay is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
  };

  const handleDownload = useReactToPrint({
    contentRef: printRef,
    documentTitle: `${tailoredResumeData?.name || 'Resume'}_Tailored_${selectedTemplate?.name?.replace(/\s+/g, '_') || 'Template'}`,
    onBeforeGetContent: () => {
      setIsDownloading(true);
      return Promise.resolve();
    },
    onAfterPrint: () => {
      setIsDownloading(false);
    },
  });

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex"
      onClick={handleBackdropClick}
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      {/* Overlay Panel */}
      <div className="ml-auto relative bg-white w-full md:w-[70%] lg:w-[60%] h-full overflow-hidden shadow-2xl transform transition-transform duration-300 ease-out translate-x-0">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Tailored Resume Preview
              </h2>
              {improvementSummary && (
                <Badge 
                  className={`${
                    improvementSummary.score_improvement > 0 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {improvementSummary.score_improvement > 0 ? '+' : ''}
                  {improvementSummary.score_improvement} points
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="h-full overflow-y-auto pb-20">
          {/* Template Selector */}
          <div className="px-6 py-4 border-b border-gray-100">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Choose Template
            </h3>
            <div className="flex gap-3 overflow-x-auto">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className={`flex-shrink-0 cursor-pointer rounded-lg border-2 transition-all ${
                    selectedTemplate?.id === template.id
                      ? "border-primary shadow-md"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => handleTemplateSelect(template)}
                >
                  <div className="p-2">
                    <Image
                      src={template.image}
                      alt={template.name}
                      width={80}
                      height={100}
                      className="rounded border object-cover"
                    />
                    <p className="text-xs text-center mt-1 font-medium">
                      {template.name}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Resume Preview */}
          <div className="px-6 py-6">
            {tailoredResumeData && selectedTemplate && (
              <div ref={printRef} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm print-container">
                {selectedTemplate.id === 1 && (
                  <BasicMainWrapper
                    name={tailoredResumeData.name}
                    title={tailoredResumeData.title}
                    contactInfo={tailoredResumeData.contact_info}
                    workExperience={tailoredResumeData.work_experience?.map((e) => ({
                      ...e,
                      period: e.dates,
                      tasks: e.responsibilities,
                      title: e.job_title,
                    }))}
                    education={tailoredResumeData.education?.map((e) => ({
                      ...e,
                      year: e.graduation_year,
                    }))}
                    skills={tailoredResumeData.skills || []}
                    profile={tailoredResumeData.professional_summary || ""}
                    projects={tailoredResumeData.projects || []}
                    achievements={tailoredResumeData.acheivements || []}
                  />
                )}
                
                {selectedTemplate.id === 2 && (
                  <ModernMainWrapper
                    name={tailoredResumeData.name}
                    title={tailoredResumeData.title}
                    contactInfo={tailoredResumeData.contact_info || {}}
                    workExperience={tailoredResumeData.work_experience?.map((e) => ({
                      ...e,
                      period: e.dates,
                      tasks: e.responsibilities,
                      title: e.job_title,
                    }))}
                    education={tailoredResumeData.education?.map((e) => ({
                      ...e,
                      year: e.graduation_year,
                    }))}
                    skills={tailoredResumeData.skills || []}
                    profile={tailoredResumeData.professional_summary || ""}
                    projects={tailoredResumeData.projects || []}
                    achievements={tailoredResumeData.acheivements || []}
                  />
                )}
                
                {selectedTemplate.id === 3 && (
                  <CreativeCardLayoutMainWrapper
                    name={tailoredResumeData.name}
                    title={tailoredResumeData.title}
                    contactInfo={tailoredResumeData.contact_info}
                    workExperience={tailoredResumeData.work_experience?.map((e) => ({
                      ...e,
                      period: e.dates,
                      tasks: e.responsibilities,
                      title: e.job_title,
                    }))}
                    education={tailoredResumeData.education?.map((e) => ({
                      ...e,
                      year: e.graduation_year,
                    }))}
                    skills={tailoredResumeData.skills || []}
                    profile={tailoredResumeData.professional_summary || ""}
                    certifications={tailoredResumeData.certifications || []}
                    projects={tailoredResumeData.projects || []}
                    achievements={tailoredResumeData.acheivements || []}
                  />
                )}
              </div>
            )}
          </div>
        </div>

        {/* Sticky Footer with Download Button */}
        <div className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
          <Button
            onClick={handleDownload}
            disabled={isDownloading || !selectedTemplate}
            className="w-full bg-primary hover:bg-primary/90"
            size="lg"
          >
            {isDownloading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Generating PDF...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Download Resume
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
