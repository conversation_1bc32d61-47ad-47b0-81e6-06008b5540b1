import React from "react";
import { User } from "lucide-react";
import { Job } from "@/app/(in)/job-search/page";

const JobCard = ({ job }) => {
  const { job_title, summary, company, location, apply_url } = job;

  return (
    <div className="relative mb-2 flex flex-col justify-between border-b border-gray-500 py-3 transition-all duration-200 ease-in-out md:flex-row group hover:bg-gray-100">
      <div className="flex flex-row w-full">
        <div className="w-12 flex items-start justify-center pt-1">
          <User className="text-green-700 w-10 h-10" />
        </div>
        <div className="ml-4 flex-1 text-left">
          <div className="mb-1 font-semibold text-lg text-primary text-left">
            {job_title}
          </div>
          <div className="text-sm mb-2 text-left">
            <span>{company} • </span>
            <span className="text-gray-700">{location}</span>
          </div>
          {summary && (
            <div className="text-sm text-foreground mb-3 text-left pr-4">
              {summary}
            </div>
          )}
        </div>
      </div>
      <div className="ml-16 mt-3 md:ml-4 md:mt-0 flex items-start">
        <a
          href={apply_url}
          target="_blank"
          rel="noopener noreferrer"
          className="rounded border-2 border-primary px-4 py-2 text-sm font-medium text-foreground bg-background transition duration-200 hover:bg-primary hover:text-primary-foreground "
          data-test="JobApplicationApplyButton"
        >
          Apply
        </a>
      </div>
    </div>
  );
};

export default JobCard;
