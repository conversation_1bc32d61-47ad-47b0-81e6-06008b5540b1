import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Copy } from "lucide-react";
import { toast } from "sonner";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";

interface CoverLetterProps {
  coverLetter: string;
  setCoverLetter?: React.Dispatch<React.SetStateAction<string>>;
}

const CoverLetter = ({ coverLetter, setCoverLetter }: CoverLetterProps) => {
  const handleCopyCoverLetter = () => {
    navigator.clipboard
      .writeText(coverLetter)
      .then(() => {
        toast.success("Cover letter copied to clipboard!");
      })
      .catch(() => {
        toast.error("Failed to copy. Please try again.");
      });
  };

  return (
    <Card className="mt-4">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-xl">Your Cover Letter</CardTitle>
        <Button
          variant="ghost"
          size="icon"
          className="cursor-pointer"
          onClick={handleCopyCoverLetter}
        >
          <Copy className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <Textarea
          value={coverLetter}
          onChange={(e) => setCoverLetter && setCoverLetter(e.target.value)}
          className="min-h-[300px] resize-none"
          readOnly={!setCoverLetter}
        />
      </CardContent>
    </Card>
  );
};

export default CoverLetter;
