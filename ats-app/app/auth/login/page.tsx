"use client";

import { createClient } from "@/utils/supabase/client";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function LoginPage() {
  const supabase = createClient();
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 100);
  }, []);

  useEffect(() => {
    const getSession = async () => {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();
      if (error) return;
      if (session?.user?.id) {
        console.log("Session found, redirecting to home page");
        window.location.href="/";
      }
    };

    getSession();
  }, [router,supabase.auth]);

  // Function to handle Google Sign-In
  const handleGoogleSignIn = async () => {
    setError(null);

    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
      });

      if (error) {
        setError(error.message);
      }
    } catch (error) {
      setError("An error occurred during Google Sign-In."+error);
    }
  };

  if (loading) return <p>Loading...</p>;
  
  return (
    <>
      <header className="py-8 flex justify-center items-center text-gray-900">
        <div className="flex flex-col items-center">
          {/* Logo with Link to Home Page */}
          <Link href="/" className="mb-2">
            <Image
              src="/images/speeduphire-logo.png"
              alt="SpeedupHire Logo"
              width={100}
              height={100}
            />
          </Link>

          <h1 className="font-bold text-2xl">
            SpeedupHire
          </h1>
        </div>
      </header>
      
      <div className="flex flex-col md:flex-row justify-center max-w-4xl mx-auto min-h-[calc(100vh-297px)]">
        {/* Left Side: Copywriting */}
        <div className="flex-1 flex flex-col justify-center items-center text-gray-800 p-8">
          <h2 className="font-bold text-center mb-4 text-xl md:text-3xl">
            Unlock Your Potential
          </h2>
          <p className="text-base md:text-lg">
            Get noticed by top employers. Sign in and optimize your resume to
            get one step closer to your dream job. Our ATS checker ensures your
            resume aligns with the latest industry standards, boosting your
            chances of landing the job.
          </p>
        </div>

        {/* Right Side: Login Buttons */}
        <div className="flex-1 flex flex-col items-center justify-center p-8">
          <h2 className="font-bold text-gray-800 mb-4 text-xl md:text-2xl">
            Login
          </h2>

          {/* Google Sign-In Button */}
          <Button 
            variant="outline" 
            className="mb-4 py-6 w-full max-w-xs hover:bg-primary hover:text-white"
            onClick={handleGoogleSignIn}
          >
            Sign in with Google
          </Button>
          
          {error && (
            <p className="text-red-500 text-sm mt-2">{error}</p>
          )}
        </div>
      </div>
    </>
  );
}