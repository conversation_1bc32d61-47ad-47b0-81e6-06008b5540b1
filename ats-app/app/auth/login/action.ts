"use server";

// import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";

import { createClient } from "@/utils/supabase/server";

// export async function login({
//   email,
//   password,
// }: {
//   email: string;
//   password: string;
// }) {
//   const supabase = await createClient();

//   // type-casting here for convenience
//   // in practice, you should validate your inputs
//   const data = {
//     email,
//     password,
//   };

//   const { data: userdata, error } = await supabase.auth.signInWithPassword(
//     data
//   );

//   if (error) {
//     redirect("/error");
//   }
//   if (data) {
//     console.log(userdata);
//   }

//   revalidatePath("/", "layout");
//   redirect("/home");
// }

// export async function signup({
//   email,
//   password,
//   fullName,
// }: {
//   email: string;
//   password: string;
//   fullName: string;
// }) {
//   const supabase = await createClient();

//   // type-casting here for convenience
//   // in practice, you should validate your inputs
//   const data = {
//     email,
//     password,
//     options: {
//       data: {
//         user_name: fullName,
//       },
//     },
//   };

//   const { error } = await supabase.auth.signUp(data);

//   if (error) {
//     redirect("/error");
//   }

//   revalidatePath("/", "layout");
//   redirect("/home");
// }

export async function signOut() {
  const supabase = await createClient();
  await supabase.auth.signOut();
  redirect("/auth/login");
}
