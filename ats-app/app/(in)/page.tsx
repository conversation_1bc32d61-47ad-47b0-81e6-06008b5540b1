"use client";

import { useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { FileUp } from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import JobSection from "@/components/dashboard/JobSearch/Job";
import Stats from "@/components/dashboard/Stats";
import QuickAction from "@/components/dashboard/QuickAction";
import RecentActivities from "@/components/dashboard/RecentActivities";
import Templates from "@/components/dashboard/Templates";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import TailorResume from "@/components/dashboard/TailorResume";

export default function Dashboard() {
  const [user, setUser] = useState<User | null>(null);

  const supabase = createClient();

  useEffect(() => {
    const getUser = async () => {
      const { data, error } = await supabase.auth.getUser();
      if (error) {
        console.error(error);
      } else {
        console.log(data.user);
        setUser(data.user);
      }
    };
    getUser();
  }, [supabase.auth]);

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">
          Welcome, {user?.user_metadata?.full_name?.split(" ")[0] || "User"}
        </h1>
        <div className="flex gap-2">
          <Link href="/review-resume">
            <Button>
              <FileUp className="w-4 h-4 mr-2 " /> Resume Review
            </Button>
          </Link>
        </div>
      </div>

      {/* Summary Stats Cards */}
      <Stats />

      {/* Action Cards */}

      <h2 className="font-semibold text-lg mb-4">Quick Actions</h2>
      <QuickAction />

      <Templates />

      {/* Job Search */}
      <JobSection jobPage={false} />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Tailor Resume to Job Description */}
        <TailorResume />

        {/* Recent Activities */}
        <RecentActivities />
      </div>
    </div>
  );
}
