"use client";

import React, { useEffect, useState, use } from "react";
import { createClient } from "@/utils/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileUp, FileSearch, FileText, RefreshCw } from "lucide-react";
import Link from "next/link";
import ATSScoreDisplay from "@/components/ATSScoreDisplay";
import ResumeReviewDisplay from "@/components/ResumeReviewDisplay";
import CoverLetter from "@/components/CoverLetter";
import ATSComparisonDisplay from "@/components/ATSComparisonDisplay";
import TailoredResumeOverlay from "@/components/TailoredResumeOverlay";

const DetailsPage = ({ params }) => {
  const { id } = use(params);
  const [activityDetails, setActivityDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showResumeOverlay, setShowResumeOverlay] = useState(false);

  const supabase = createClient();

  // Get icon based on activity type
  const getActivityIcon = (type) => {
    switch (type) {
      case "resume-scanned":
        return <FileUp className="h-6 w-6 text-blue-500" />;
      case "resume-tailored":
        return <RefreshCw className="h-6 w-6 text-green-500" />;
      case "resume-reviewed":
        return <FileSearch className="h-6 w-6 text-teal-500" />;
      case "cover-letter":
        return <FileText className="h-6 w-6 text-purple-500" />;
      default:
        return <FileText className="h-6 w-6 text-gray-500" />;
    }
  };

  useEffect(() => {
    const fetchActivityDetails = async () => {
      if (!id) return;

      try {
        setLoading(true);

        const { data: sessionData } = await supabase.auth.getSession();
        const token = sessionData?.session?.access_token;

        if (!token) {
          throw new Error("No authentication token available");
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/api/activities/${id}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }

        const data = await response.json();
        setActivityDetails(data);
      } catch (err) {
        setError(
          (err instanceof Error ? err.message : "An unknown error occurred") ||
            "Failed to fetch activity details"
        );
        console.error("Error fetching activity details:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchActivityDetails();
  }, [id, supabase.auth]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <p>Loading activity details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-96">
        <p className="text-red-500 mb-4">Error: {error}</p>
        <Link href="/dashboard">
          <Button>Return to Dashboard</Button>
        </Link>
      </div>
    );
  }

  // Get activity type from the fetched details
  const type = activityDetails?.type || "";

  const handleViewTailoredResume = () => {
    if (activityDetails?.details?.data?.tailored_resume) {
      setShowResumeOverlay(true);
    }
  };

  const handleCloseResumeOverlay = () => {
    setShowResumeOverlay(false);
  };

  const handleDownloadTailoredResume = () => {
    // Download functionality is now handled within TailoredResumeOverlay component
    console.log('Download triggered from comparison display');
  };

  // Render the appropriate content based on activity type
  const renderActivityContent = () => {
    if (!activityDetails) {
      return <p>No details available for this activity.</p>;
    }

    switch (type) {
      case "resume-scanned":
        if (activityDetails.details) {
          const activityData = activityDetails.details.data;

          // Check if it's enhanced format (has overall_score) or legacy format
          const isEnhancedFormat = activityData && activityData.overall_score !== undefined;

          let atsResponse;

          if (isEnhancedFormat) {
            // Enhanced format - pass the complete data with resume_authenticity at top level
            atsResponse = {
              ...activityData,
              resume_id: activityDetails.details.resumeId || activityDetails.resumeId || "",
              resume_authenticity: activityData.resume_authenticity || "Not verified",
            };
          } else {
            // Legacy format - construct the old structure with null checks
            atsResponse = {
              data: {
                final_ats_score: activityDetails.details.atsScore || 0,
                score_breakdown:
                  activityData?.score_breakdown?.score_breakdown ||
                  activityData?.score_breakdown ||
                  [],
              },
              resume_authenticity: activityData?.resume_authenticity || "Not verified",
              title: activityData?.title || "Resume Scanned",
              resume_id: activityDetails.details.resumeId || activityDetails.resumeId || "",
            };
          }

          return <ATSScoreDisplay atsResponse={atsResponse} />;
        }
        break;

      case "resume-tailored":
        if (activityDetails.details?.data) {
          const comparisonData = {
            tailored_resume: activityDetails.details.data.tailored_resume,
            original_ats_score: activityDetails.details.data.original_ats_score,
            tailored_ats_score: activityDetails.details.data.tailored_ats_score,
            improvement_summary: activityDetails.details.data.improvement_summary,
          };

          return (
            <ATSComparisonDisplay
              comparisonData={comparisonData}
              onViewTailoredResume={handleViewTailoredResume}
              onDownloadTailoredResume={handleDownloadTailoredResume}
            />
          );
        }
        break;

      case "resume-reviewed":
        if (activityDetails.details) {
          // Format data to match ResumeReviewDisplay component requirements
          const reviewResponse = {
            data: activityDetails.details.data,
          };
          return <ResumeReviewDisplay reviewResponse={reviewResponse} />;
        }
        break;

      case "cover-letter":
        if (activityDetails.details) {
          const data = activityDetails.details.data;
          const coverLetter = `${data?.introduction}\n\n${data?.body}\n\n${data?.conclusion}`;
          return <CoverLetter coverLetter={coverLetter} />;
        }
        break;

      default:
        return (
          <div className="space-y-6">Details not found for this activity.</div>
        );
    }

    // Fallback for when activity details don't match expected format
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-lg font-semibold mb-2">Activity Information</h2>
          <div className="bg-gray-50 p-4 rounded-md overflow-auto">
            <pre className="text-sm whitespace-pre-wrap">
              {JSON.stringify(activityDetails, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="container mx-auto py-8">
        <Link href="/" className="mb-6 block">
          <Button variant="outline">← Back to Dashboard</Button>
        </Link>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-gray-100 p-3 rounded-lg">
                {getActivityIcon(type)}
              </div>
              <div>
                <h1 className="text-2xl font-bold">
                  {type
                    ?.replace(/_/g, " ")
                    .replace(/\b\w/g, (l) => l.toUpperCase())}{" "}
                  Details
                </h1>
              </div>
            </div>

            {renderActivityContent()}
          </CardContent>
        </Card>
      </div>

      {/* Tailored Resume Overlay */}
      <TailoredResumeOverlay
        isOpen={showResumeOverlay}
        onClose={handleCloseResumeOverlay}
        tailoredResumeData={activityDetails?.details?.data?.tailored_resume}
        improvementSummary={activityDetails?.details?.data?.improvement_summary}
      />
    </>
  );
};

export default DetailsPage;
