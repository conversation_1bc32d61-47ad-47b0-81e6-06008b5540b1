"use client";
import React, { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  FileUp,
  FileText,
  BarChart2,
  Briefcase,
  Clock,
  ChevronLeft,
  ChevronRight,
  Filter,
  X,
  FileSearch, // Added for resume review icon
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import Link from "next/link";

interface ActivityDetails {
  atsScore?: number;
  companyName?: string;
  jobTitle?: string;
  summary?: string;
  feedback?: string; // Added for resume review feedback
}

interface Activity {
  id: string;
  type: string;
  title: string;
  date: string;
  details: ActivityDetails;
  activity_id:string
}

export default function HistoryPage() {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [dateFrom, setDateFrom] = useState<Date | undefined>(undefined);
  const [dateTo, setDateTo] = useState<Date | undefined>(undefined);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Changed from 10 to 8 activities per page
  const itemsPerPage = 8;
  const supabase = createClient();

  // Date limits
  const today = new Date();
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(today.getMonth() - 1);

  // Utility to normalize activity type from API
  const normalizeType = (type: string): string => type.replace(/-/g, "_");

  // Get all available activity types - Added resume_reviewed to the list
  const activityTypes = [
    { value: "resume_scanned", label: "Resume Scanned" },
    { value: "resume_tailored", label: "Resume Tailored" },
    { value: "resume_reviewed", label: "Resume Reviewed" }, // Added new activity type
    { value: "job_applied", label: "Job Applied" },
    { value: "cover_letter", label: "Cover Letter" },
    { value: "job_saved", label: "Job Saved" },
  ];

  // Get icon based on activity type - Added resume_reviewed icon
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "resume_scanned":
      case "resume_tailored":
        return <FileUp className="h-5 w-5 text-blue-500" />;
      case "resume_reviewed":
        return <FileSearch className="h-5 w-5 text-teal-500" />; // Added new icon
      case "job_applied":
        return <Briefcase className="h-5 w-5 text-green-500" />;
      case "cover_letter":
        return <FileText className="h-5 w-5 text-purple-500" />;
      case "job_saved":
        return <BarChart2 className="h-5 w-5 text-orange-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  // Format date to display in a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();

    if (date.toDateString() === now.toDateString()) {
      return `Today at ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })}`;
    }

    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday at ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })}`;
    }

    return (
      date.toLocaleDateString() +
      " at " +
      date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  // Get type label from value
  const getTypeLabel = (value: string) => {
    const type = activityTypes.find((type) => type.value === value);
    return type ? type.label : value;
  };

  // Toggle type selection in filter
  const toggleTypeSelection = (type: string) => {
    if (selectedTypes.includes(type)) {
      setSelectedTypes(selectedTypes.filter((t) => t !== type));
    } else {
      setSelectedTypes([...selectedTypes, type]);
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchQuery("");
    setDateFrom(undefined);
    setDateTo(undefined);
    setSelectedTypes([]);
    setCurrentPage(1);
  };

  // Custom date validation for start date
  const isStartDateDisabled = (date: Date) => {
    return date < oneMonthAgo || date > today;
  };

  // Custom date validation for end date
  const isEndDateDisabled = (date: Date) => {
    const minDate = dateFrom ? dateFrom : oneMonthAgo;
    return date < minDate || date > today;
  };

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  useEffect(() => {
    const fetchActivities = async () => {
      setIsLoading(true);
      setError(null);

      if (!accessToken) {
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/api/activities?mode=history`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }

        const data = await response.json();
        const normalized = (data.activities || [])
          .map((activity: Activity) => ({
            ...activity,
            type: normalizeType(activity.type),
          }))
          .filter((activity: Activity) => {
            // Filter out the resume_scan and job_application types
            return (
              activity.type !== "resume_scan" &&
              activity.type !== "job_application"
            );
          });

        setActivities(normalized);
        setFilteredActivities(normalized);
      } catch (err) {
        console.error("Error fetching activities:", err);
        setError("Failed to load activities. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    if (accessToken) {
      fetchActivities();
    }
  }, [accessToken]);

  // Apply filters
  useEffect(() => {
    let result = [...activities];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (activity) =>
          activity.title.toLowerCase().includes(query) ||
          (activity.details?.companyName &&
            activity.details.companyName.toLowerCase().includes(query)) ||
          (activity.details?.jobTitle &&
            activity.details.jobTitle.toLowerCase().includes(query)) ||
          // Added search in feedback for resume reviews
          (activity.details?.feedback &&
            activity.details.feedback.toLowerCase().includes(query))
      );
    }

    // Apply date range filter
    if (dateFrom) {
      result = result.filter((activity) => new Date(activity.date) >= dateFrom);
    }

    if (dateTo) {
      // Set time to end of day for inclusive filtering
      const endDate = new Date(dateTo);
      endDate.setHours(23, 59, 59, 999);
      result = result.filter((activity) => new Date(activity.date) <= endDate);
    }

    // Apply type filter
    if (selectedTypes.length > 0) {
      result = result.filter((activity) =>
        selectedTypes.includes(activity.type)
      );
    }

    setFilteredActivities(result);
    setCurrentPage(1);
  }, [searchQuery, dateFrom, dateTo, selectedTypes, activities]);

  // Calculate pagination values
  const totalPages = Math.ceil(filteredActivities.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentActivities = filteredActivities.slice(startIndex, endIndex);

  // Navigate between pages
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 text-primary">
          Activity History
        </h1>
        <p className="text-gray-500">View and manage your past activities</p>
      </div>

      <div className="mb-6 flex flex-col md:flex-row md:items-center gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative">
            <Input
              placeholder="Search activities..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 w-full sm:w-64"
            />
            <div className="absolute left-2 top-2.5 text-gray-400">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
              </svg>
            </div>
          </div>

          <Button
            variant={showFilters ? "default" : "outline"}
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter size={16} />
            Filters
            {(selectedTypes.length > 0 || dateFrom || dateTo) && (
              <Badge className="ml-1 bg-blue-500 text-white">
                {selectedTypes.length + (dateFrom ? 1 : 0) + (dateTo ? 1 : 0)}
              </Badge>
            )}
          </Button>
        </div>

        {filteredActivities.length > 0 && (
          <div className="text-sm text-gray-500">
            Showing {startIndex + 1} to{" "}
            {Math.min(endIndex, filteredActivities.length)} of{" "}
            {filteredActivities.length} activities
          </div>
        )}
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-wrap justify-between gap-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Activity Type</h3>
                <div className="flex flex-wrap gap-2">
                  {activityTypes.map((type) => (
                    <div
                      key={type.value}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`type-${type.value}`}
                        checked={selectedTypes.includes(type.value)}
                        onCheckedChange={() => toggleTypeSelection(type.value)}
                      />
                      <label
                        htmlFor={`type-${type.value}`}
                        className="text-sm cursor-pointer"
                      >
                        {type.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Date Range</h3>
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex justify-start text-left font-normal w-36"
                      >
                        {dateFrom
                          ? format(dateFrom, "MMM d, yyyy")
                          : "Start date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateFrom}
                        onSelect={setDateFrom}
                        disabled={isStartDateDisabled}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <div className="flex items-center">to</div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex justify-start text-left font-normal w-36"
                      >
                        {dateTo ? format(dateTo, "MMM d, yyyy") : "End date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateTo}
                        onSelect={setDateTo}
                        disabled={isEndDateDisabled}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="flex items-center">
                <Button
                  variant="ghost"
                  onClick={resetFilters}
                  className="h-8 px-2 text-sm"
                >
                  <X size={14} className="mr-1" />
                  Clear filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isLoading ? (
        <Card>
          <CardContent className="p-6 flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-800 mx-auto mb-4"></div>
              <p>Loading your activities...</p>
            </div>
          </CardContent>
        </Card>
      ) : error ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-500 py-8">
              <p>{error}</p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="mt-4"
              >
                Try again
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {filteredActivities.length > 0 ? (
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {currentActivities.map((activity) => (
                    <div
                      key={activity.id}
                      className="p-4 sm:p-6 hover:bg-gray-50 transition-colors flex flex-col sm:flex-row gap-4"
                    >
                      <div className="bg-gray-100 p-3 rounded-full h-12 w-12 flex items-center justify-center flex-shrink-0">
                        {getActivityIcon(activity.type)}
                      </div>

                      <div className="flex-grow">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 justify-between">
                          <div>
                            <Link href={`/details/${activity.activity_id}`}>
                              <h3 className="font-medium text-lg hover:text-blue-600 cursor-pointer transition-colors">
                                {activity.title}
                              </h3>
                            </Link>
                            <p className="text-gray-500 text-sm">
                              {formatDate(activity.date)}
                            </p>
                          </div>

                          <Badge variant="outline" className="w-fit">
                            {getTypeLabel(activity.type)}
                          </Badge>
                        </div>

                        {activity.details && (
                          <div className="mt-3 space-y-2">
                            {activity.details.companyName && (
                              <p className="text-sm">
                                <span className="font-medium">Company:</span>{" "}
                                {activity.details.companyName}
                              </p>
                            )}

                            {activity.details.jobTitle && (
                              <p className="text-sm">
                                <span className="font-medium">Position:</span>{" "}
                                {activity.details.jobTitle}
                              </p>
                            )}

                            {activity.details.atsScore !== undefined && (
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">
                                  ATS Score:
                                </span>
                                <span
                                  className={`px-2 py-1 rounded text-sm font-medium ${
                                    activity.details.atsScore >= 80
                                      ? "bg-green-100 text-green-800"
                                      : activity.details.atsScore >= 50
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-orange-100 text-orange-800"
                                  }`}
                                >
                                  {activity.details.atsScore}%
                                </span>
                              </div>
                            )}

                            {activity.type === "resume_reviewed" &&
                              activity.details.feedback && (
                                <div className="mt-2">
                                  <p className="text-sm font-medium mb-1">
                                    Feedback:
                                  </p>
                                  <div className="bg-gray-50 p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                    {activity.details.feedback}
                                  </div>
                                </div>
                              )}

                            {activity.details.summary && (
                              <p className="text-sm text-gray-600 mt-2">
                                {activity.details.summary}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-12">
                <div className="text-center">
                  <Clock className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-medium mb-2">
                    No activities found
                  </h3>
                  <p className="text-gray-500 mb-6">
                    {searchQuery ||
                    dateFrom ||
                    dateTo ||
                    selectedTypes.length > 0
                      ? "No activities match your current filters"
                      : "You haven't performed any activities yet"}
                  </p>

                  {(searchQuery ||
                    dateFrom ||
                    dateTo ||
                    selectedTypes.length > 0) && (
                    <Button onClick={resetFilters} variant="outline">
                      Clear Filters
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pagination */}
          {filteredActivities.length > itemsPerPage && (
            <div className="flex justify-center mt-6">
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter(
                    (page) =>
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                  )
                  .map((page, index, array) => (
                    <React.Fragment key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="px-2">...</span>
                      )}
                      <Button
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => goToPage(page)}
                      >
                        {page}
                      </Button>
                    </React.Fragment>
                  ))}

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}