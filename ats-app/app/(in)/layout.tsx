import AppSidebar from "@/components/sidebar/AppSidebar";
import Header from "@/components/sidebar/Header"
import { SidebarProvider } from "@/components/ui/sidebar"
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body suppressHydrationWarning>
        <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <AppSidebar />
          <main className="flex flex-col w-full">
            <Header />

            {children}
          </main>
        </div>
        </SidebarProvider>
      </body>
    </html>
  );
}
