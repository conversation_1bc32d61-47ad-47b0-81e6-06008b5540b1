"use client";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import BasicMainWrapper from "@/components/resumes/classic-professional/MainWrapper";
import ModernMainWrapper from "@/components/resumes/modern-two-columns/MainWrapper";
import CreativeCardLayoutMainWrapper from "@/components/resumes/creative-card-layout/MainWrapper";
import ExportResumeButton from "@/components/resumes/ExportButton";
import { createClient } from "@/utils/supabase/client";
import Image from "next/image";
import { useParams } from "next/navigation";

export default function GenerateResume() {
  const { id } = useParams();
  console.log(id);
  const supabase = createClient();
  const [selectedTemplate, setSelectedTemplate] = useState(
    null
  );
  const [accessToken, setAccessToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const [resumeData, setResumeData] = useState();
  const [, setToasterData] = useState({
    message: "",
    severity: "success",
    open: false,
  });

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  // Fetch resume data when the component mounts or when params.id changes
  useEffect(() => {
    const fetchResumeData = async () => {
      if (!accessToken) return;
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/api/trailor-resume/${id}`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (!response.ok) {
          if (response.status === 429) {
            setToasterData({
              message: "Rate limit exceeded. Please try again later.",
              severity: "warning",
              open: true,
            });
          } else {
            throw new Error("Network response was not ok");
          }
        } else {
          const data = await response.json();
          setIsLoading(false);
          setResumeData(data?.parsed_response || {});
        }
      } catch (err) {
        setToasterData({
          message: "Error calculating ATS score. Please try again.\n" + err,
          severity: "error",
          open: true,
        });
      }
    };
    fetchResumeData();
  }, [accessToken, id]);

  // List of 5 sample resume templates
  const templates = [
    {
      id: 1,
      name: "Classic Professional",
      image: "/images/resume-thumbnails/classic-professional.png",
    },
    {
      id: 2,
      name: "Modern Two-Column",
      image: "/images/resume-thumbnails/modern-two-column.png",
    },
    {
      id: 3,
      name: "Creative Card Layout",
      image: "/images/resume-thumbnails/creative-card-layout.png",
    },
    // {
    //   id: 4,
    //   name: "Elegant Grid Design",
    //   image: "/images/resume-thumbnails/elegant-grid-design.png",
    // },
    // {
    //   id: 5,
    //   name: "Bold and Simple",
    //   image: "/images/resume-thumbnails/bold-and-simple.png",
    // },
  ];

  // Handle template selection
  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    // Resume builder section will appear below after selection
  };

  const [isSmallScreen, setIsSmallScreen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768); // Similar to theme.breakpoints.down('md')
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);

  return (
    <div className="p-5 paddingX-none min-h-[calc(100vh-124.04px-48px)]">
      {/* Display all templates side by side */}
      {isLoading ? (
        <h4 className="text-xl font-semibold">Loading...</h4>
      ) : (
        <>
          <h4
            className={`text-2xl font-bold mb-4 export-hidden ${
              isSmallScreen ? "mt-8" : ""
            }`}
          >
            Select a Resume Template
          </h4>

          <div
            className={`export-hidden flex flex-wrap gap-6 mt-5 ${
              isSmallScreen ? "justify-center" : ""
            }`}
          >
            {templates.map((template) => (
              <Card
                key={template.id}
                className={`text-center p-2.5 cursor-pointer ${
                  isSmallScreen ? "w-[70%] mb-2" : "w-[18%]"
                } 
                  ${selectedTemplate?.id === template.id ? "shadow-lg" : ""} 
                  hover:shadow-md transition-shadow`}
                onClick={() => handleTemplateSelect(template)}
              >
                <CardContent className="p-0 pt-2">
                  <Image
                    src={template.image}
                    alt={template.name}
                    width={300}
                    height={300}
                    className="w-full h-auto"
                  />
                  <h6 className="text-lg font-semibold mt-2.5">
                    {template.name}
                  </h6>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}

      {/* Resume Builder Section */}
      {selectedTemplate && (
        <div className="mt-10 flex flex-wrap justify-between gap-1 no-padding-margin-top">
          <div className="w-full flex justify-between items-center export-hidden">
            <h5 className="text-xl font-semibold">
              Now Editing: {selectedTemplate.name}
            </h5>
            <ExportResumeButton />

          </div>

          {selectedTemplate?.id === 1 && resumeData &&(
            <BasicMainWrapper
              name={resumeData.name }
              title={resumeData.title }
              contactInfo={resumeData.contact_info }
              workExperience={resumeData.work_experience?.map((e) => ({
                ...e,
                period: e.dates,
                tasks: e.responsibilities,
                title: e.job_title,
              }))}
              education={resumeData.education?.map((e) => ({
                ...e,
                year: e.graduation_year,
              }))}
              
              skills={resumeData.skills || []}
              profile={resumeData.professional_summary || ""}
              projects={resumeData.projects || []}
              achievements={resumeData.acheivements || []}
            />
          )}
          {selectedTemplate?.id === 2 && resumeData && (
            <ModernMainWrapper
              name={resumeData.name}
              title={resumeData.title}
              contactInfo={resumeData.contact_info || {}}
              workExperience={resumeData.work_experience?.map((e) => ({
                ...e,
                period: e.dates,
                tasks: e.responsibilities,
                title: e.job_title,
              }))}
              education={resumeData.education?.map((e) => ({
                ...e,
                year: e.graduation_year,
              }))}
              skills={resumeData.skills || []}
              profile={resumeData.professional_summary || ""}
              projects={resumeData.projects || []}
              achievements={resumeData.acheivements || []}
            />
          )}
          {selectedTemplate?.id === 3 && resumeData && (
            <CreativeCardLayoutMainWrapper
              name={resumeData.name || "Alex Johnson"}
              title={resumeData.title || "UI/UX Designer"}
              contactInfo={resumeData.contact_info}
              workExperience={resumeData.work_experience?.map((e) => ({
                ...e,
                period: e.dates,
                tasks: e.responsibilities,
                title: e.job_title,
              }))}
              education={resumeData.education?.map((e) => ({
                ...e,
                year: e.graduation_year,
              }))
              }
              skills={resumeData.skills || []}
              profile={resumeData.professional_summary || ""}
              certifications={resumeData.certifications || []}
              projects={resumeData.projects || []}
              achievements={resumeData.acheivements || []}
            />
          )}
        </div>
      )}
    </div>
  );
}
