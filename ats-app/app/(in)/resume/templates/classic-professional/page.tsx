"use client";
import React from "react";
import ExportResumeButton from "@/components/resumes/ExportButton";
import MainWrapper from "@/components/resumes/classic-professional/MainWrapper";

interface WorkExperienceItem {
  title: string;
  company: { text: string; url?: string };
  location: string;
  period: string;
  tasks: string[];
}

interface Education {
  degree: string;
  institution: { text: string; url?: string };
  year: string;
}

const initialSkills: string[] = [
  "Wireframing & Prototyping (Figma, Sketch)",
  "Responsive Web Design",
  "User Research & Testing",
  "HTML, CSS, JavaScript",
  "Adobe Creative Suite",
];

const education: Education[] = [{
  degree: "Bachelor of Science in Computer Science",
  institution:{text: "University of California, Berkeley"},
  year: "Graduated: 2016",
}];

const initialWorkExperience: WorkExperienceItem[] = [
  {
    title: "Senior Full-Stack Developer",
    company:{text: "ABC Tech Solutions"},
    location: "San Francisco, CA",
    period: "June 2019 - Present",
    tasks: [
      "Designed and implemented scalable web applications using React.js and Node.js.",
      "Led a team of 5 developers to deliver features on time.",
      "Collaborated with the UI/UX team to create intuitive and responsive user interfaces.",
      "Optimized application performance by 20% through refactoring code and updating database queries.",
    ],
  },
  {
    title: "Junior Developer",
    company:{text: "XYZ Innovations"},
    location: "New York, NY",
    period: "Jan 2017 - May 2019",
    tasks: [
      "Developed REST APIs for the backend using Node.js and Express.",
      "Integrated third-party services like Stripe and SendGrid for payments and email notifications.",
      "Wrote unit and integration tests for critical application features.",
    ],
  },
];

const profile: string = `Experienced Full-Stack Developer with 5+ years of expertise in
    designing and building scalable web applications. Proficient in
    JavaScript, React.js, Node.js, and database management. Passionate
    about creating user-friendly interfaces and writing clean, efficient
    code.`;

const ResumePreview: React.FC = () => {
  
  return (
    <div className='max-w-4xl mx-auto mt-10 no-margin-top'>
      {/* Header Section: Template Name and Export Button */}
      <div className='flex justify-between items-center mb-6 no-padding-margin export-hidden'>
        <h1 className='text-xl md:text-2xl font-bold text-gray-800 template-name'>
          Template Name: Classic Professional
        </h1>
        <ExportResumeButton  />
      </div>
      <MainWrapper
        education={education}
        skills={initialSkills}
        workExperience={initialWorkExperience}
        profile={profile}
        name='John Doe'
        title='Full Stack Developer'
        contactInfo={{
          email: "<EMAIL>",
          phone: "(+123) 456 789",
          location: "San Jose, CA",
        }}
      />
    </div>
  );
};

export default ResumePreview;