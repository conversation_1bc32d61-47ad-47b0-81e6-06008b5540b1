"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
  CardHeader,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";

// Define the type for resume templates
interface ResumeTemplate {
  id: number;
  image: string;
  caption: string;
  description: string;
  url: string;
  tags?: string[];
}

// Sample data: Local list of resume templates
const resumeTemplates: ResumeTemplate[] = [
  {
    id: 1,
    image: "/images/resume-thumbnails/classic-professional.png",
    caption: "Classic Professional",
    description:
      "A minimalist, ATS-friendly design with a single-column layout and clean typography.",
    url: "/resume/templates/classic-professional",
    tags: ["ATS-Friendly", "Minimal", "Professional"],
  },
  {
    id: 2,
    image: "/images/resume-thumbnails/modern-two-column.png",
    caption: "Modern Two-Column",
    description:
      "A balanced two-column layout with structured sections, ideal for showcasing skills and experience side by side.",
    url: "/resume/templates/modern-two-columns",
    tags: ["Modern", "Two-Column", "Structured"],
  },
  {
    id: 3,
    image: "/images/resume-thumbnails/creative-card-layout.png",
    caption: "Creative Card Layout",
    description:
      "A visually engaging template with card-style sections for key skills, work experience, and education, perfect for a modern, sleek presentation.",
    url: "/resume/templates/creative-card-layout",
    tags: ["Creative", "Card-Style", "Modern"],
  },

  // {
  //   id: 4,
  //   image: "/images/resume-thumbnails/elegant-grid-design.png",
  //   caption: "Elegant Grid Design",
  //   description:
  //     "A responsive grid-based resume with alternating background colors and subtle accents, offering a professional yet visually appealing structure.",
  //   url: "/resume/templates/elegant-grid-design",
  // },
  // {
  //   id: 5,
  //   image: "/images/resume-thumbnails/bold-and-simple.png",
  //   caption: "Bold and Simple",
  //   description:
  //     "A full-width, single-column layout with color accents for headers and a modern feel, combining simplicity and style while remaining ATS-compatible.",
  //   url: "/resume/templates/bold-and-simple",
  // },
];

function HomePage() {
  const router = useRouter();

  // Handle card click
  const handlePreview = (url: string) => {
    router.push(url);
  };

  return (
    <div className="container py-8 mx-auto ">
      <h1 className="text-3xl font-bold text-center mb-8">
        Choose Your Resume Template
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {resumeTemplates.map((template) => (
          <Card
            key={template.id}
            className="group  overflow-hidden border-1 shadow-md hover:shadow-xl transition-all duration-300"
          >
            <CardHeader className="p-0 overflow-hidden">
              <div className="relative h-64 w-full">
                <Image
                  src={template.image}
                  alt={template.caption}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0   opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center">
                  <Button
                    className="mb-6 translate-y-4 group-hover:translate-y-0 transition-transform duration-300"
                    onClick={() => handlePreview(template.url)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Preview Template
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="pt-5 pb-3">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-xl font-semibold text-green-700">
                  {template.caption}
                </h3>
              </div>
              <p className="text-gray-600 text-sm">{template.description}</p>
            </CardContent>

            <CardFooter className="flex flex-wrap gap-2 pt-0">
              {template.tags?.map((tag, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="bg-green-50 text-green-700 hover:bg-green-100"
                >
                  {tag}
                </Badge>
              ))}
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default HomePage;
