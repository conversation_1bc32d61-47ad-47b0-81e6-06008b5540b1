"use client";

import React from "react";
import MainWrapper from "@/components/resumes/modern-two-columns/MainWrapper";
import ExportResumeButton from "@/components/resumes/ExportButton";

// === Resume Data ===
const profile = `Creative front-end developer with over 4 years of experience in building responsive websites. Skilled in HTML, CSS, JavaScript, and modern frameworks like React.js. Passionate about delivering high-quality user experiences through clean and efficient code.`;

const contactInfo = {
  email: "<EMAIL>",
  phone: "(*************",
  location: "Los Angeles, CA",
};

const education = [{
  degree: "Bachelor of Science in Computer Science",
  institution:{text: "University of Southern California"},
  year: "Graduated 2018",
}];

const skills = [
  "HTML5 & CSS3",
  "JavaScript (ES6+)",
  "React.js, Next.js",
  "Responsive Web Design",
  "Cross-Browser Compatibility",
  "Git & GitHub",
];

const workExperience = [
  {
    title: "Front-End Developer",
    company:{text: "Tech Innovations Co."},
    location: "Los Angeles, CA",
    period: "July 2019 - Present",
    tasks: [
      "Developed and maintained the company's customer-facing website using React.js and Tailwind CSS.",
      "Collaborated with the UI/UX team to implement pixel-perfect designs and improve user experience.",
      "Optimized website performance, reducing load time by 30%.",
    ],
  },
];

const certifications = [
  "Certified Front-End Developer - FreeCodeCamp (2020)",
];

// === Component ===
const SecondTemplate = () => (
  <div className='max-w-4xl mx-auto mt-10'>
    <div className='flex justify-between items-center mb-6 export-hidden'>
      <h1 className='text-xl md:text-2xl font-bold text-gray-800'>
        Template Name: Modern Two-Column
      </h1>
      <ExportResumeButton />
    </div>

    <MainWrapper
      name='Jane Smith'
      title='Front-End Developer'
      profile={profile}
      contactInfo={contactInfo}
      education={education}
      skills={skills}
      workExperience={workExperience}
      certifications={certifications}
    />
  </div>
);

export default SecondTemplate;
