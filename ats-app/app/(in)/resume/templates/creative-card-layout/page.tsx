"use client";
import React from "react";
import ExportResumeButton from "@/components/resumes/ExportButton";
import MainWrapper from "@/components/resumes/creative-card-layout/MainWrapper";

// Define interfaces for type safety

interface WorkExperienceItem {
  title: string;
  company: {text: string, url?: string};
  location: string;
  period: string;
  tasks: string[];
}

interface EducationItem {
  degree: string;
  institution:{text: string, url?: string};
  year: string;
}


// Initial data
const initialSkills: string[] = [
  "Wireframing & Prototyping (Figma, Sketch)",
  "Responsive Web Design",
  "User Research & Testing",
  "HTML, CSS, JavaScript",
  "Adobe Creative Suite",
];

const education: EducationItem[] = [{
  degree: "Bachelor of Arts in Graphic Design",
  institution:{text: "University of Texas, Austin"},
  year: "2016",
}];

const initialWorkExperience: WorkExperienceItem[] = [
  {
    title: "Senior UI/UX Designer",
    company:{text: "Creative Labs"},
    location: "Austin, TX",
    period: "August 2018 - Present",
    tasks: [
      "Led the design of web and mobile applications, improving user satisfaction by 30% based on surveys.",
      "Collaborated with the product team to create high-fidelity prototypes using Figma and Sketch.",
      "Conducted user testing sessions to gather feedback and iterate on design solutions.",
    ],
  },
  {
    title: "UI/UX Designer",
    company:{text: "Bright Digital Solutions"},
    location: "Austin, TX",
    period: "June 2016 - July 2018",
    tasks: [
      "Designed responsive web interfaces for various clients in the healthcare and education sectors.",
      "Collaborated with developers to ensure seamless implementation of design specifications.",
      "Participated in client meetings to discuss design requirements and present mockups.",
    ],
  },
];

const certifications: string[] = ["Certified UX Designer - Nielsen Norman Group (2020)"];

const profile: string = `Experienced UI/UX designer with 6+ years of experience in creating intuitive and aesthetically pleasing user interfaces. Adept at collaborating with developers and stakeholders to deliver high-quality digital products.`;

const SecondTemplate: React.FC = () => {
  return (
    <div className='max-w-4xl mx-auto mt-10 no-margin-top'>
      {/* Header Section: Template Name and Export Button */}
      <div className='flex justify-between items-center mb-6 no-padding-margin export-hidden'>
        <h1 className='text-xl md:text-2xl font-bold text-gray-800 template-name'>
          Template Name: Creative Card Layout
        </h1>
        <ExportResumeButton />
      </div>
      <MainWrapper
        profile={profile}
        skills={initialSkills}
        education={education}
        workExperience={initialWorkExperience}
        certifications={certifications}
        name='Alex Johnson'
        title='UI/UX Designer'
        contactInfo={{
          email: "<EMAIL>",
          phone: "(+123) 456 789",
          location: "Austin, TX",
        }}
      />
    </div>
  );
};

export default SecondTemplate;