"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Upload, ArrowRight, Zap, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";
import ATSScoreDisplay from "@/components/ATSScoreDisplay";
import ATSComparisonDisplay from "@/components/ATSComparisonDisplay";
import TailoredResumeOverlay from "@/components/TailoredResumeOverlay";

// Legacy format interfaces
interface ScoreCategory {
  category: string;
  adjusted_score: number;
  weightage: number;
  explanation: string;
  improvements: string[];
}

// Enhanced format interfaces
interface ATSSectionScore {
  score: number;
  status: string;
  feedback: string;
  improvements: string[];
  examples?: string[];
  priority: string;
  weightage: number;
}

// Comparison data interface
interface ComparisonData {
  tailored_resume: unknown; // The structured resume data
  original_ats_score: ATSResponse;
  tailored_ats_score: ATSResponse;
  improvement_summary: {
    score_improvement: number;
    original_score: number;
    tailored_score: number;
  };
}

// Combined interface that handles both legacy and enhanced formats
export interface ATSResponse {
  resume_id: string;
  resume_authenticity: string;
  title: string;

  // Legacy format fields
  data?: {
    final_ats_score: number;
    score_breakdown: ScoreCategory[];
  };

  // Enhanced format fields
  overall_score?: number;
  job_match_percentage?: number;
  experience_level?: string;
  job_title?: string;
  company_name?: string;

  // Enhanced section scores
  experience_level_match?: ATSSectionScore;
  skills_match?: ATSSectionScore;
  education_match?: ATSSectionScore;
  job_specific_keywords?: ATSSectionScore;
  achievements_responsibilities?: ATSSectionScore;
  industry_relevance?: ATSSectionScore;
  certifications_training?: ATSSectionScore;

  // Enhanced recommendations
  top_priorities?: string[];
  quick_wins?: string[];
  strengths?: string[];
  overall_summary?: string;
}

export default function UploadPage() {
  const supabase = createClient();
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [jobDescription, setJobDescription] = useState("");
  const [saveResume, setSaveResume] = useState(false);
  const [atsResponse, setAtsResponse] = useState<ATSResponse | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{
    resume?: string;
    jobDescription?: string;
  }>({});

  // New state for tailoring functionality
  const [isTailoring, setIsTailoring] = useState(false);
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [showComparison, setShowComparison] = useState(false);
  const [showResumeOverlay, setShowResumeOverlay] = useState(false);

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  const validateForm = () => {
    const errors: { resume?: string; jobDescription?: string } = {};
    
    if (!resumeFile) {
      errors.resume = "Please upload a resume file";
    }
    
    if (!jobDescription.trim()) {
      errors.jobDescription = "Please provide a job description";
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setResumeFile(file);
      // Clear resume validation error when file is uploaded
      if (validationErrors.resume) {
        setValidationErrors(prev => ({ ...prev, resume: undefined }));
      }
      toast.success("Resume uploaded successfully");
    }
  };

  const handleJobDescriptionChange = (value: string) => {
    setJobDescription(value);
    // Clear job description validation error when user starts typing
    if (validationErrors.jobDescription && value.trim()) {
      setValidationErrors(prev => ({ ...prev, jobDescription: undefined }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    const formData = new FormData();
    formData.append("resume", resumeFile!);
    formData.append("job_description", jobDescription);
    formData.append("save_resume", saveResume.toString());

    try {
      setIsLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/api/upload-resume`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          body: formData,
        }
      );

      if (response.status === 429) {
        toast.warning("Rate limit exceeded. Please try again later.");
        return;
      }

      if (!response.ok) {
        throw new Error("Unauthorized");
      }

      const data = await response.json();
      setAtsResponse(data);
      toast.success("ATS score calculated successfully!");
    } catch (error) {
      console.error(error);
      toast.error("Error calculating ATS score. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleTailorResume = async () => {
    if (!atsResponse?.resume_id) {
      toast.error("No resume data available for tailoring");
      return;
    }

    try {
      setIsTailoring(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/api/tailor-resume-with-ats/${atsResponse.resume_id}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (response.status === 429) {
        toast.warning("Rate limit exceeded. Please try again later.");
        return;
      }

      if (!response.ok) {
        throw new Error("Failed to tailor resume");
      }

      const data = await response.json();
      setComparisonData(data);
      setShowComparison(true);
      toast.success("Resume tailored successfully! Check the comparison below.");
    } catch (error) {
      console.error(error);
      toast.error("Error tailoring resume. Please try again.");
    } finally {
      setIsTailoring(false);
    }
  };

  const handleViewTailoredResume = () => {
    if (comparisonData?.tailored_resume) {
      setShowResumeOverlay(true);
    }
  };

  const handleCloseResumeOverlay = () => {
    setShowResumeOverlay(false);
  };

  const handleDownloadTailoredResume = () => {
    // Download functionality is now handled within TailoredResumeOverlay component
    console.log('Download triggered from comparison display');
  };

  return (
    <>
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-2 text-primary">Upload Your Resume</h1>
        <p className="text-gray-500 mb-6">
          Upload your resume and job description to check your ATS score.
        </p>

        <div className="grid gap-6">
          <div>
            <Label htmlFor="resume-upload" className="mb-2 text-primary block">
              Resume *
            </Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className={`w-full sm:w-auto ${
                  validationErrors.resume ? 'border-red-500' : ''
                }`}
                onClick={() =>
                  document.getElementById("resume-upload")?.click()
                }
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Resume
              </Button>
              <Input
                id="resume-upload"
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                onChange={handleResumeUpload}
              />
            </div>
            {resumeFile && (
              <p className="text-sm text-green-600 mt-2">
                ✓ {resumeFile.name} uploaded
              </p>
            )}
            {validationErrors.resume && (
              <p className="text-sm text-red-500 mt-2">
                {validationErrors.resume}
              </p>
            )}
          </div>

          <div>
            <Label
              htmlFor="job-description"
              className="mb-2 text-primary block"
            >
              Job Description *
            </Label>
            <Textarea
              id="job-description"
              placeholder="Paste the job description here..."
              value={jobDescription}
              onChange={(e) => handleJobDescriptionChange(e.target.value)}
              className={`h-32 ${
                validationErrors.jobDescription ? 'border-red-500' : ''
              }`}
            />
            {validationErrors.jobDescription && (
              <p className="text-sm text-red-500 mt-2">
                {validationErrors.jobDescription}
              </p>
            )}
          </div>

          <div className="flex items-start space-x-2">
            <Checkbox
              id="save-resume"
              checked={saveResume}
              onCheckedChange={(checked) => setSaveResume(checked as boolean)}
            />
            <Label
              htmlFor="save-resume"
              className="text-sm text-gray-500 leading-relaxed"
            >
              I agree to SpeedUpHire securely storing my uploaded resume for
              personalized job recommendations, ATS analysis, and application
              tracking. I understand that I can delete my resume at any time
              from my account settings.
            </Label>
          </div>

          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            {isLoading ? "Generating..." : "Check ATS Score"}
          </Button>
        </div>
      </div>
    </div>
    <div>
      {atsResponse && (
        <>
          <Card className="mt-4 mr-2">
            <CardContent className="pt-6">
              <ATSScoreDisplay atsResponse={atsResponse} />
            </CardContent>
          </Card>

          {/* Enhanced Tailor Resume Action Card */}
          {!showComparison && (
            <Card className="mt-6 mr-2 border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-blue-50">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center space-x-2">
                    <Zap className="w-5 h-5 text-primary" />
                    <h3 className="text-lg font-semibold text-primary">Ready to Improve Your Score?</h3>
                  </div>
                  <p className="text-gray-600 max-w-md mx-auto">
                    Get a tailored resume that matches this job description perfectly and see your before/after ATS score comparison.
                  </p>
                  <Button
                    size="lg"
                    className="bg-primary hover:bg-primary/90"
                    onClick={handleTailorResume}
                    disabled={isTailoring}
                  >
                    {isTailoring ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Tailoring Resume...
                      </>
                    ) : (
                      <>
                        <ArrowRight className="w-4 h-4 mr-2" />
                        Tailor My Resume & Compare Scores
                      </>
                    )}
                  </Button>
                  <p className="text-xs text-gray-500">
                    ✨ AI-powered resume optimization with before/after ATS score analysis
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* ATS Comparison Display */}
          {showComparison && comparisonData && (
            <div className="mt-6 mr-2">
              <ATSComparisonDisplay
                comparisonData={comparisonData}
                onViewTailoredResume={handleViewTailoredResume}
                onDownloadTailoredResume={handleDownloadTailoredResume}
              />
            </div>
          )}
        </>
      )}

      {/* Tailored Resume Overlay */}
      <TailoredResumeOverlay
        isOpen={showResumeOverlay}
        onClose={handleCloseResumeOverlay}
        tailoredResumeData={comparisonData?.tailored_resume}
        improvementSummary={comparisonData?.improvement_summary}
      />
    </div>
  </>
  );
}