"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Upload } from "lucide-react";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";
import ResumeReviewDisplay from "@/components/ResumeReviewDisplay";

// Enhanced interface for the new review format
export interface ReviewSectionScore {
  score: number;
  status: string;
  feedback: string;
  improvements: string[];
  examples?: string[];
  priority: string;
}

// Updated interface to match both old and new API response formats
export interface ResumeReviewData {
  title: string;

  // Enhanced format fields
  overall_score?: number;
  experience_level?: string;
  resume_type?: string;
  top_priorities?: string[];
  quick_wins?: string[];
  long_term_goals?: string[];
  strengths?: string[];
  overall_summary?: string;

  // Section reviews (can be either string for old format or ReviewSectionScore for new format)
  formatting_and_layout?: string | ReviewSectionScore;
  header_and_contact_info?: string | ReviewSectionScore;
  skills_summary?: string | ReviewSectionScore;
  work_experience?: string | ReviewSectionScore;
  education?: string | ReviewSectionScore;
  certifications?: string | ReviewSectionScore;
  projects?: string | ReviewSectionScore;
  achievements?: string | ReviewSectionScore;
  language_grammar_tone?: string | ReviewSectionScore;
  ats_compatibility?: string | ReviewSectionScore;

  [key: string]: unknown;
}

export interface ReviewResponse {
  data: ResumeReviewData;
}

export default function ReviewResumePage() {
  const supabase = createClient();
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [saveResume, setSaveResume] = useState(false);
  const [reviewResponse, setReviewResponse] = useState<ReviewResponse | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [validationError, setValidationError] = useState<string>("");

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  const validateForm = () => {
    if (!resumeFile) {
      setValidationError("Please upload a resume file");
      return false;
    }
    
    setValidationError("");
    return true;
  };

  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setResumeFile(file);
      // Clear validation error when file is uploaded
      if (validationError) {
        setValidationError("");
      }
      toast.success("Resume uploaded successfully");
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    const formData = new FormData();
    formData.append("resume", resumeFile!);
    formData.append("save_resume", saveResume.toString());

    try {
      setIsLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/api/review-resume`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          body: formData,
        }
      );

      if (response.status === 429) {
        toast.warning("Rate limit exceeded. Please try again later.");
        return;
      }

      if (!response.ok) {
        throw new Error("Unauthorized");
      }

      const data = await response.json();
      setReviewResponse(data);
      toast.success("Resume review completed successfully!");
    } catch (error) {
      console.error(error);
      toast.error("Error reviewing resume. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-2 text-primary">Resume Review</h1>
        <p className="text-gray-500 mb-6">
          Upload your resume to get a professional assessment and actionable feedback.
        </p>

        <div className="grid gap-6">
          <div>
            <Label htmlFor="resume-upload" className="mb-2 text-primary block">
              Resume *
            </Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className={`w-full sm:w-auto ${
                  validationError ? 'border-red-500' : ''
                }`}
                onClick={() =>
                  document.getElementById("resume-upload")?.click()
                }
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Resume
              </Button>
              <Input
                id="resume-upload"
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                onChange={handleResumeUpload}
              />
            </div>
            {resumeFile && (
              <p className="text-sm text-green-600 mt-2">
                ✓ {resumeFile.name} uploaded
              </p>
            )}
            {validationError && (
              <p className="text-sm text-red-500 mt-2">
                {validationError}
              </p>
            )}
          </div>

          <div className="flex items-start space-x-2">
            <Checkbox
              id="save-resume"
              checked={saveResume}
              onCheckedChange={(checked) => setSaveResume(checked as boolean)}
            />
            <Label
              htmlFor="save-resume"
              className="text-sm text-gray-500 leading-relaxed"
            >
              I agree to SpeedUpHire securely storing my uploaded resume for
              personalized job recommendations, resume analysis, and application
              tracking. I understand that I can delete my resume at any time
              from my account settings.
            </Label>
          </div>

          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            {isLoading ? "Reviewing..." : "Review My Resume"}
          </Button>
        </div>
      </div>
    </div>
    <div>
      {reviewResponse && (
        <Card className="mt-4 mr-2">
          <CardContent className="pt-6">
            <ResumeReviewDisplay reviewResponse={reviewResponse} />
          </CardContent>
        </Card>
      )}
    </div>
  </>
  );
}