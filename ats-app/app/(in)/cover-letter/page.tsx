"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload } from "lucide-react";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";
import CoverLetter from "@/components/CoverLetter";

export default function CoverLetterBuilder() {
  const supabase = createClient();
  const [jobDescription, setJobDescription] = useState("");
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [coverLetter, setCoverLetter] = useState("");
  const [tone, setTone] = useState("Formal");
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{
    resume?: string;
    jobDescription?: string;
  }>({});

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  const validateForm = () => {
    const errors: { resume?: string; jobDescription?: string } = {};
    
    if (!resumeFile) {
      errors.resume = "Please upload a resume file";
    }
    
    if (!jobDescription.trim()) {
      errors.jobDescription = "Please provide a job description";
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (file && allowedTypes.includes(file.type) && file.size <= maxSize) {
      setResumeFile(file);
      // Clear resume validation error when file is uploaded
      if (validationErrors.resume) {
        setValidationErrors(prev => ({ ...prev, resume: undefined }));
      }
      toast.success("Resume uploaded successfully");
    } else {
      toast.error("Unsupported file type or file size exceeds 5MB");
    }
  };

  const handleJobDescriptionChange = (value: string) => {
    setJobDescription(value);
    // Clear job description validation error when user starts typing
    if (validationErrors.jobDescription && value.trim()) {
      setValidationErrors(prev => ({ ...prev, jobDescription: undefined }));
    }
  };

  const handleCoverLetterGeneration = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("job_description", jobDescription);
      formData.append("tone", tone);
      formData.append("resume", resumeFile!);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URI}/api/generate-cover-letter`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = "Failed to generate cover letter.";
        if (response.status === 400) errorMessage = "Bad Request. Please check your input.";
        if (response.status === 500) errorMessage = "Server error. Please try again later.";
        throw new Error(errorMessage);
      }

      const { data } = await response.json();
      
      const fullCoverLetter = `${data?.introduction}\n\n${data?.body}\n\n${data?.conclusion}`;
      setCoverLetter(fullCoverLetter);

      toast.success("Cover letter generated successfully!");
    } catch (error) {
      toast.error((error instanceof Error) ? error.message : "Error generating cover letter. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 text-gray-500 px-4">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl text-primary font-bold mb-2">Generate Your Cover Letter</h1>
        <p className="text-gray-500 mb-6">
          Upload your resume, paste the job description, select a tone, and generate a tailored cover letter.
        </p>

        <div className="grid gap-6">
          <div>
            <Label htmlFor="resume-upload" className="mb-2 block text-primary">Resume *</Label>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                className={`w-full sm:w-auto ${
                  validationErrors.resume ? 'border-red-500' : ''
                }`}
                onClick={() => document.getElementById('resume-upload')?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Resume
              </Button>
              <Input
                id="resume-upload"
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                onChange={handleResumeUpload}
              />
            </div>
            {resumeFile && (
              <p className="text-sm text-green-600 mt-2">
                ✓ {resumeFile.name} uploaded
              </p>
            )}
            {validationErrors.resume && (
              <p className="text-sm text-red-500 mt-2">
                {validationErrors.resume}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="job-description" className="mb-2 block text-primary">Job Description *</Label>
            <Textarea
              id="job-description"
              placeholder="Paste the job description here..."
              value={jobDescription}
              onChange={(e) => handleJobDescriptionChange(e.target.value)}
              className={`h-32 ${
                validationErrors.jobDescription ? 'border-red-500' : ''
              }`}
            />
            {validationErrors.jobDescription && (
              <p className="text-sm text-red-500 mt-2">
                {validationErrors.jobDescription}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="tone-select" className="mb-2 block text-primary">Tone</Label>
            <Select
              value={tone}
              onValueChange={setTone}
            >
              <SelectTrigger id="tone-select" className="w-full sm:w-72">
                <SelectValue placeholder="Select tone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Formal">Formal</SelectItem>
                <SelectItem value="Enthusiastic">Enthusiastic</SelectItem>
                <SelectItem value="Professional">Professional</SelectItem>
                <SelectItem value="Conversational">Conversational</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button 
            onClick={handleCoverLetterGeneration} 
            disabled={isLoading}
            className="w-full sm:w-auto cursor-pointer"
          >
            {isLoading ? "Generating..." : "Generate Cover Letter"}
          </Button>

          {coverLetter && (
            <CoverLetter 
              coverLetter={coverLetter} 
              // setCoverLetter is optional now, so we can include or exclude it based on our needs
              setCoverLetter={setCoverLetter}
            /> 
          )}
        </div>
      </div>
    </div>
  );
}