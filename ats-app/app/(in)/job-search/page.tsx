"use client";
// import React, { useState, useRef, useEffect } from "react";
// import JobCard from "@/components/JobCard";
// import { createClient } from "@/utils/supabase/client";

// const JobsPage: React.FC = () => {
//   const supabase = createClient();
//   const [fileName, setFileName] = useState<string>("");
//   const [isDragging, setIsDragging] = useState<boolean>(false);
//   const [jobs, setJobs] = useState<Job[]>([]);
//   const [isLoading, setIsLoading] = useState<boolean>(false);
//   const [error, setError] = useState<string>("");
//   const [uploadedFile, setUploadedFile] = useState<File | null>(null);
//   const fileInputRef = useRef<HTMLInputElement | null>(null);
//   const [accessToken, setAccessToken] = useState<string | null>(null);

//   useEffect(() => {
//     const getSession = async () => {
//       const { data } = await supabase.auth.getSession();
//       const token = data?.session?.access_token;
//       if (token) {
//         setAccessToken(token);
//       }
//     };
//     getSession();
//   }, []);

//   const validFileTypes = ["application/pdf"];

//   const validateFile = (file: File): boolean => {
//     if (file && validFileTypes.includes(file.type)) {
//       setFileName(file.name);
//       setUploadedFile(file);
//       return true;
//     } else {
//       setError("Please upload a PDF file only.");
//       return false;
//     }
//   };

//   const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setError("");
//     const file = e.target.files?.[0];
//     if (file) {
//       validateFile(file);
//     }
//   };

//   const handleUpload = async () => {
//     if (!uploadedFile) return;

//     setIsLoading(true);
//     setError("");

//     try {
//       const formData = new FormData();
//       formData.append("resume", uploadedFile);

//       const response = await fetch(`${process.env.NEXT_PUBLIC_API_URI}/api/deep-job-search`, {
//         method: "POST",
//         headers: {
//           "Authorization": `Bearer ${accessToken}`,
//         },
//         body: formData,
//       });

//       if (!response.ok) {
//         throw new Error(`Server responded with ${response.status}`);
//       }

//       const data = await response.json();
//       setJobs(data.jobs || []);
//     } catch (err) {
//       setError("Failed to upload resume and fetch jobs. Please try again.");
//       console.error(err);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <>
//       <div className="w-full px-10 text-center lg:w-auto xl:px-20">
//         <h2 className="text-2xl font-bold md:mb-10 md:text-5xl mt-8">
//           Discover Your Next Opportunity
//         </h2>

//         <div className="mx-auto max-w-2xl bg- rounded-lg shadow-md p-6 mb-10">
//           <h3 className="text-xl text-gray-700 font-semibold mb-4">Upload Your Resume</h3>
//           <p className="text-gray-600 mb-6">Find the right job faster with AI-powered search tailored to your resume!</p>

//           {uploadedFile ? (
//             <div className="flex flex-col items-center">
//               <p className="text-gray-500 font-medium mb-4">{fileName}</p>
//               <button
//                 className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-green-700 transition-colors"
//                 onClick={() => {
//                   setUploadedFile(null);
//                   setFileName("");
//                 }}
//               >
//                 Upload New File
//               </button>
//             </div>
//           ) : (
//             <div
//               className={`border-2 border-dashed rounded-lg p-8 transition-colors ${
//                 isDragging ? "border-gray-500 bg-gray-50" : "border-gray-300"
//               }`}
//               onDragOver={(e) => {
//                 e.preventDefault();
//                 setIsDragging(true);
//               }}
//               onDragLeave={() => setIsDragging(false)}
//               onDrop={(e) => {
//                 e.preventDefault();
//                 setIsDragging(false);
//                 setError("");
//                 validateFile(e.dataTransfer.files[0]);
//               }}
//               onClick={() => fileInputRef.current?.click()}
//             >
//               <div className="flex flex-col items-center justify-center space-y-4">
//                 <svg
//                   className="w-12 h-12 text-gray-400"
//                   fill="none"
//                   stroke="currentColor"
//                   viewBox="0 0 24 24"
//                   xmlns="http://www.w3.org/2000/svg"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth="2"
//                     d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
//                   />
//                 </svg>
//                 <p className="text-gray-500">Drag and drop your resume or click to browse</p>
//                 <input ref={fileInputRef} type="file" className="hidden" accept=".pdf" onChange={handleFileChange} />
//                 <button className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-green-700 transition-colors">
//                   Browse Files
//                 </button>
//                 <p className="text-xs text-gray-500">Accepted format: PDF (Max 5MB)</p>
//               </div>
//             </div>
//           )}

//           {error && (
//             <div className="mt-4 p-3 bg-red-100 text-destructive rounded-md">
//               {error}
//             </div>
//           )}

//           <div className="mt-6 flex justify-center">
//             <button
//               className={`px-6 py-2 rounded-md font-medium transition-colors ${
//                 uploadedFile && !isLoading
//                   ? "bg-primary hover:bg-green-700 text-white"
//                   : "bg-gray-200 text-gray-400 cursor-not-allowed"
//               }`}
//               disabled={!uploadedFile || isLoading}
//               onClick={handleUpload}
//             >
//               {isLoading ? (
//                 <span className="flex items-center">
//                   <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
//                     <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                     <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
//                   </svg>
//                   Processing...
//                 </span>
//               ) : uploadedFile ? "Upload" : "Select a File"}
//             </button>
//           </div>
//         </div>

//         {jobs.length > 0 && (
//           <div className="mt-10 mx-auto max-w-4xl">
//             <h3 className="text-2xl font-bold mb-6 text-left">Jobs Matched to Your Resume</h3>
//             <div>
//               {jobs.map((job, index) => (
//                 <JobCard key={index} job={job} />
//               ))}
//             </div>
//           </div>
//         )}
//       </div>
//     </>
//   );
// };

// export default JobsPage;

import React from "react";
import JobSection from "@/components/dashboard/JobSearch/Job";


export interface Job {
  id: string;
  job_title: string;
  company: string;
  location: string;
  summary: string;
  apply_url: string;
}

const JobsPage = () => {



  return (
    <div className="p-6">
      <div className="w-full px-10 text-center lg:w-auto xl:px-20">
        <h2 className="text-2xl text-primary font-bold md:mb-10 md:text-5xl mt-8">
          Discover Your Next Opportunity
        </h2>
      </div>
      <JobSection jobPage={true}/>
    </div>
  );
};

export default JobsPage;
