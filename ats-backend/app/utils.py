import logging
import json
import os
import requests
from duckduckgo_search import DDGS

def calculate_ats_score(categories):
    """
    Calculate the final ATS score and provide a detailed breakdown for each category.
    If the combined weightage exceeds 100%, scale the weightages proportionally.
    Optional categories can have 0% weightage if not applicable.

    Args:
    categories (list of dict): A list of categories with raw scores and weightages.
        Each category is a dictionary with the following keys:
        - 'category': The name of the category (string)
        - 'score': The raw score (0-100) for the category (int)
        - 'weightage': The weightage assigned to the category (float)
        - 'explanation': A textual explanation of the score (string)
        - 'improvements': A list of improvement suggestions (list of strings)

    Returns:
    dict: A dictionary with the final ATS score and the breakdown for each category.
    """
    
    # Filter out categories with 0% weightage (optional categories)
    applicable_categories = [c for c in categories if c['weightage'] > 0 ]

    # Calculate total weightage
    total_weightage = sum(c['weightage'] for c in applicable_categories)

    # Scale weightages if total exceeds 100%
    if total_weightage > 100:
        scaling_factor = 100 / total_weightage
        for c in applicable_categories:
            c['scaled_weightage'] = c['weightage'] * scaling_factor
    else:
        for c in applicable_categories:
            c['scaled_weightage'] = c['weightage']

    # Calculate the adjusted score for each category and keep track of the breakdown
    final_ats_score = 0
    breakdown = []

    for c in applicable_categories:
        adjusted_score = (c['score'] / 100) * c['scaled_weightage']
        final_ats_score += adjusted_score
        breakdown.append({
            'category': c['category'],
            'adjusted_score': round(adjusted_score, 2),
            'weightage': round(c['scaled_weightage'], 2),
            'explanation': c.get('explanation', ''),
            'improvements': c.get('improvements', [])
        })

    return {
        'final_ats_score': round(final_ats_score, 2),
        'score_breakdown': breakdown
    }


def search_brave(query):
    """Calls Brave Search API to fetch search results."""
    url = "https://api.search.brave.com/res/v1/web/search"
    headers = {
        "Accept": "application/json",
        "Accept-Encoding": "gzip",
        "X-Subscription-Token": os.getenv("BRAVE_API_KEY")
    }
    params = {"q": query}

    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()  # Raise an error for bad status codes (4xx, 5xx)
        return response.json()  # Return parsed JSON response

    except requests.exceptions.RequestException as e:
        print(f"Error calling Brave Search API: {e}")
        return None

def search_duckduckgo(query, max_results=10):
    """Searches DuckDuckGo and returns top job links from Wellfound and Indeed."""
    try:
        with DDGS() as ddgs:

            results = ddgs.text(query, max_results=max_results)
            job_listings = [
                {"title": result["title"], "url": result["href"], "snippet": result["body"]}
                for result in results
            ]
            return job_listings
    except Exception as e:
        print(f"Error fetching jobs from DuckDuckGo: {e}")
        return []


def search_serper(query, api_key, max_results=10):
    try:
        url = "https://google.serper.dev/search"
        
        payload = json.dumps({
            "q": query
        })
        
        headers = {
            'X-API-KEY': api_key,
            'Content-Type': 'application/json'
        }
        
        response = requests.request("POST", url, headers=headers, data=payload)
        response_data = response.json()
        
        # Extract organic search results
        if 'organic' in response_data and len(response_data['organic']) > 0:
            job_listings = []
            
            for result in response_data['organic'][:max_results]:
                job_info = {
                    "title": result.get("title", ""),
                    "url": result.get("link", ""),
                    "snippet": result.get("snippet", "")
                }
                
                # Add date if available
                if "date" in result:
                    job_info["date"] = result["date"]
                
                job_listings.append(job_info)
                
            return job_listings
        else:
            return []
            
    except Exception as e:
        print(f"Error fetching jobs from Serper: {e}")
        return []