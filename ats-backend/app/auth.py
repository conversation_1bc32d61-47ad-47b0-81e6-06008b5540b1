import logging
import os

from typing import Optional

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import SecurityScopes, HTTPAuthorizationCredentials, HTTPBearer

class UnauthorizedException(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Requires authentication"
        )

class VerifyToken:
  """Does all firebase auth token verification using PyJWT"""
  async def verify(self,
    security_scopes: SecurityScopes,
    token: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer())
  ):
    if token is None:
        raise UnauthorizedException()

    else:
      try:
        jwt_secret = os.getenv("JWT_SECRET")
        issuer_url = os.getenv("ISSUER_URL")
        try:
          payload = jwt.decode(token.credentials.encode('utf-8'), jwt_secret, algorithms=['HS256'], audience = "authenticated")
          user_id = payload['sub']
          logging.info(f"decoded user id:{user_id}")
          if not user_id or user_id == "" or payload['iss'] != issuer_url:
            raise UnauthorizedException()
        except jwt.ExpiredSignatureError:
           logging.error("token expired")
           raise UnauthorizedException()
        
      except Exception as error:
          logging.error(f"Error verify jwt token: {str(error)}")
          raise UnauthorizedException()
    return payload