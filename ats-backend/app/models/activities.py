from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, UUID, func
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime,timezone
from typing import Optional, Dict, Any, Literal
from pydantic import BaseModel
import uuid

Base = declarative_base()

class Activity(Base):
    __tablename__ = "ats_activities"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(String, index=True, nullable=False)  
    type = Column(String, nullable=False)  
    title = Column(String, nullable=False)
    date = Column(DateTime, default=lambda: datetime.now(timezone.utc))  # Always UTC
    details = Column(JSONB, nullable=True)  
    activity_id = Column(UUID(as_uuid=True), unique=True, index=True, default=uuid.uuid4)

class ActivityBase(BaseModel):
    type: Literal["resume-scanned", "resume-tailored", "cover-letter", "job-applied","resume-reviewed"]  # Specify the allowed types
    title: str
    details: Dict[str, Any]