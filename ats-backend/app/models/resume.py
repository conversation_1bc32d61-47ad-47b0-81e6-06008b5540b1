from sqlalchemy import Column, String, Text, DateTime, TIMESTAMP, func,UUID,ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field
from typing import List, Optional
import uuid

Base = declarative_base()

class ResumeModel(Base):
    __tablename__ = "resumes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_name = Column(String, nullable=False)
    created_by = Column(UUID(as_uuid=True),nullable=False)
    phone_number = Column(String)
    email = Column(String)
    skills = Column(JSONB)  
    experience = Column(JSONB) 
    projects = Column(JSONB)  
    content = Column(Text)  
    content_hash = Column(String, unique=True)
    created_at = Column(TIMESTAMP, server_default= func.now())

    ats_entries = relationship("AtsModel", back_populates="resume")

class SkillItem(BaseModel):
    title: Optional[str] = None
    description: str

class ExperienceItem(BaseModel):
    company: str
    position: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    location: Optional[str] = None

class ProjectItem(BaseModel):
    name: str
    description: str
    url: Optional[str] = None

class ResumeParseResponse(BaseModel):
    candidate_name: str = Field(..., description="Full name of the candidate")
    phone_number: str = Field(..., description="Contact phone number")
    email: str = Field(..., description="Email address")
    skills: List[SkillItem] = Field(..., description="List of skills with optional titles and descriptions")
    experience: List[ExperienceItem] = Field(..., description="Work experience history")
    projects: List[ProjectItem] = Field(..., description="Projects completed by the candidate")
    content: str = Field(..., description="Complete resume content formatted in markdown")

class CourseRecommendation(BaseModel):
    platform: str = Field(description="Platform name: 'Udemy', 'Coursera', or 'Skillshare'")
    course_title: str = Field(description="Specific course title")
    course_url: Optional[str] = Field(description="Direct link to the course", default=None)
    skill_addressed: str = Field(description="The specific skill gap this course addresses")
    estimated_duration: Optional[str] = Field(description="Course duration (e.g., '6 weeks', '20 hours')", default=None)
    difficulty_level: str = Field(description="Course difficulty: 'Beginner', 'Intermediate', or 'Advanced'")
    priority: str = Field(description="Priority level: 'high', 'medium', or 'low'")
    reason: str = Field(description="Why this course is recommended based on the job requirements")

# Legacy model for backward compatibility
class ScoreBreakdown(BaseModel):
    category: str = Field(description="Different categories on which the resume is assessed")
    score: int = Field(description="Score the resume for this category out of 100")
    weightage: int = Field(description="weightage percentage this category has based on the JD")
    explanation: str
    improvements: list[str] = Field(description="list of improvements the candidate can make to improve their resume for this category")

# Enhanced ATS Score Models
class ATSSectionScore(BaseModel):
    score: int = Field(description="Score out of 100 for this section")
    status: str = Field(description="Status: 'excellent', 'good', 'needs_improvement', or 'poor'")
    feedback: str = Field(description="Detailed feedback about how well this section matches the job requirements")
    improvements: list[str] = Field(description="Specific, actionable improvement suggestions")
    examples: list[str] = Field(description="Concrete examples or templates for improvement", default=[])
    priority: str = Field(description="Priority level: 'high', 'medium', or 'low'")
    weightage: float = Field(description="Final weightage percentage used for this category")
    course_recommendations: List[CourseRecommendation] = Field(
        description="Recommended courses to address skill gaps (only if courses are needed)", 
        default=[]
    )

class EnhancedATSScoreResponse(BaseModel):
    # Job and candidate info
    job_title: str = Field(description="Extracted job title from job description")
    company_name: str = Field(description="Extracted company name from job description", default="")
    experience_level: str = Field(description="Detected experience level: 'fresher', 'mid-level', or 'senior'")

    # Overall assessment
    overall_score: int = Field(description="Overall ATS score out of 100")
    job_match_percentage: int = Field(description="How well the resume matches the specific job (0-100)")

    # Section-wise detailed scores
    experience_level_match: ATSSectionScore
    skills_match: ATSSectionScore
    education_match: ATSSectionScore
    job_specific_keywords: ATSSectionScore
    achievements_responsibilities: ATSSectionScore
    industry_relevance: ATSSectionScore
    certifications_training: ATSSectionScore

    # Actionable recommendations
    top_priorities: list[str] = Field(description="Top 3-5 most important improvements to make")
    quick_wins: list[str] = Field(description="Easy improvements that can be made quickly")
    strengths: list[str] = Field(description="Key strengths that match the job well")
    
    # Course recommendations summary
    priority_courses: List[CourseRecommendation] = Field(
        description="High-priority course recommendations across all sections", 
        default=[]
    )
    total_recommended_courses: int = Field(
        description="Total number of courses recommended across all sections", 
        default=0
    )

    # Summary
    resume_authenticity: str = Field(description="Assessment of resume authenticity")
    overall_summary: str = Field(description="Comprehensive summary with next steps")
    title: str = Field(description="Title starting with 'Resume Scanned for ...'")

    def model_post_init(self, __context) -> None:
        """Calculate total courses and extract priority courses after model initialization"""
        all_courses = []
        
        # Collect all course recommendations from all sections
        for section in [
            self.experience_level_match,
            self.skills_match,
            self.education_match,
            self.job_specific_keywords,
            self.achievements_responsibilities,
            self.industry_relevance,
            self.certifications_training
        ]:
            all_courses.extend(section.course_recommendations)
        
        # Set total count
        self.total_recommended_courses = len(all_courses)
        
        # Extract high-priority courses
        self.priority_courses = [
            course for course in all_courses 
            if course.priority == 'high'
        ]

# Legacy response for backward compatibility
class ATSScoreResponse(BaseModel):
    resume_authenticity: str = Field(description="Explain the authenticity of the resume")
    score_breakdown: list[ScoreBreakdown]
    title: str = Field(description="Title of the resume starting with 'Resume Scanned ..'")

class AtsModel(Base):
    __tablename__ = "ats"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    resume_id = Column(String, ForeignKey("resumes.id"), nullable=False)
    job_description = Column(String)
    ats = Column(JSONB)
    created_at = Column(TIMESTAMP, server_default=func.now())

    resume = relationship("ResumeModel", back_populates="ats_entries")

# Enhanced Resume Review Models
class ReviewSectionScore(BaseModel):
    score: int = Field(description="Score out of 100 for this section")
    status: str = Field(description="Status: 'excellent', 'good', 'needs_improvement', or 'poor'")
    feedback: str = Field(description="Detailed feedback about what's working and what's not")
    improvements: list[str] = Field(description="Specific, actionable improvement suggestions")
    examples: list[str] = Field(description="Concrete examples or templates for improvement", default=[])
    priority: str = Field(description="Priority level: 'high', 'medium', or 'low'")
    course_recommendations: List[CourseRecommendation] = Field(
        description="Recommended courses to improve this section (only if courses are needed)", 
        default=[]
    )

class SectionWeight(BaseModel):
    section: str = Field(description="Section name")
    weight: float = Field(description="Weight percentage (0-100)")
    base_weight: float = Field(description="Original base weight percentage")
    adjustment: float = Field(description="Adjustment made (+/- percentage)")
    reasoning: str = Field(description="Why this adjustment was made")

class ResumeReviewResponse(BaseModel):
    # Section-wise detailed reviews
    formatting_and_layout: ReviewSectionScore
    header_and_contact_info: ReviewSectionScore
    skills_summary: ReviewSectionScore
    work_experience: ReviewSectionScore
    education: ReviewSectionScore
    certifications: ReviewSectionScore
    projects: ReviewSectionScore
    achievements: ReviewSectionScore
    language_grammar_tone: ReviewSectionScore
    ats_compatibility: ReviewSectionScore

    # Overall assessment
    overall_score: int = Field(description="Overall resume score out of 100")
    experience_level: str = Field(description="Detected experience level: 'fresher', 'mid-level', or 'senior'")
    resume_type: str = Field(description="Resume type: 'technical', 'business', 'creative', etc.")

    # Weightage system
    section_weights: list[SectionWeight] = Field(description="Final weights used for each section with reasoning")
    weightage_reasoning: str = Field(description="Overall explanation of weightage adjustments made")

    # Prioritized recommendations
    top_priorities: list[str] = Field(description="Top 3-5 most important improvements to make")
    quick_wins: list[str] = Field(description="Easy improvements that can be made quickly")
    long_term_goals: list[str] = Field(description="Improvements that require more effort but high impact")

    # Course recommendations summary
    priority_courses: List[CourseRecommendation] = Field(
        description="High-priority course recommendations across all sections", 
        default=[]
    )
    total_recommended_courses: int = Field(
        description="Total number of courses recommended across all sections", 
        default=0
    )

    # Summary and next steps
    strengths: list[str] = Field(description="Key strengths of the resume")
    overall_summary: str = Field(description="Comprehensive summary with next steps")
    title: str = Field(description="Title starting with 'Resume Reviewed ..'")

    def model_post_init(self, __context) -> None:
        """Calculate total courses and extract priority courses after model initialization"""
        all_courses = []
        
        # Collect all course recommendations from all sections
        for section in [
            self.formatting_and_layout,
            self.header_and_contact_info,
            self.skills_summary,
            self.work_experience,
            self.education,
            self.certifications,
            self.projects,
            self.achievements,
            self.language_grammar_tone,
            self.ats_compatibility
        ]:
            all_courses.extend(section.course_recommendations)
        
        # Set total count
        self.total_recommended_courses = len(all_courses)
        
        # Extract high-priority courses
        self.priority_courses = [
            course for course in all_courses 
            if course.priority == 'high'
        ]