from datetime import datetime
import hashlib
import asyncio
from services.serve import client, redis_client
import os
from time import time, sleep
import json
import uuid
import requests
import logging
from typing import Dict
from PyPDF2 import PdfReader
import openai
import io
from fastapi import FastAPI, File, UploadFile, HTTPException, Body, Security, Request, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from utils import calculate_ats_score, search_duckduckgo
from auth import VerifyToken
from search_agent import summarize_jobs
from pydantic import BaseModel, Field
from typing import List, Optional
from services.serve import client
from services.resume import ResumeService
from services.job import JobService
from services.activities import ActivityService, ActivityBase

from prompts import  SYSTEM_PROMPT_TRAILOR_RESUME, SYSTEM_PROMPT_COVER_LETTER_BUILDER 

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class LinkText(BaseModel):
    text: str
    url: Optional[str] = None

class ContactInfo(BaseModel):
    email: str
    phone: str
    location: str

class Certification(BaseModel):
    name: str
    institution: str
    year: str

class Project(BaseModel):
    name: LinkText  # Project name might have a GitHub link
    description: str
    technologies: List[str]
    dates: str

class WorkExperience(BaseModel):
    job_title: str
    company: LinkText  # Company name might have a company website link
    location: str
    dates: str
    responsibilities: List[str]

class Education(BaseModel):
    degree: str
    institution: LinkText  # Institution might have a university website link
    graduation_year: str

class Acheivement(BaseModel):
    title: str
    tasks: List[str]
    date: Optional[str] = None 

class ATSTailoredResumeTemplate(BaseModel):
    name: str
    title: str
    contact_info: ContactInfo
    professional_summary: str
    skills: List[str]
    education: List[Education]
    work_experience: List[WorkExperience]
    projects: Optional[List[Project]] 
    certifications: Optional[List[Certification]] 
    acheivements: Optional[List[Acheivement]]
    activity_title: str = Field(description="Title of the activity starting with 'Resume Tailored for ..'")

class GenerateCoverLetterRequest(BaseModel):
    job_description: str
    tone: str

class CoverLetterTemplate(BaseModel):
    introduction: str
    body: str
    conclusion: str
    title: str = Field(description="Title of the cover letter starting with 'Cover Letter generated for ..'")
    position: str = Field(description="Position applied for")
    company: str = Field(description="Company applied to")

class JobSearchQueries(BaseModel):
    query: str
# Replace with your actual OpenAI API key





app = FastAPI()
auth = VerifyToken()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


# Configuration
RATE_LIMIT = 3  # Maximum number of requests allowed
TIME_WINDOW = 60  # Time window in seconds (1 minute)

# In-memory dictionary to store request count and timestamps for each client
request_counts: Dict[str, Dict[str, float]] = {}

def get_client_id(request: Request, user_id: str) -> str:
    """Generate a client identifier based on the client IP and user ID."""
    client_ip = request.client.host  # Get the client's IP address
    client_id = f"{client_ip}:{user_id}"  # Use a combination of IP and user ID
    return client_id

def is_rate_limited(client_id: str) -> bool:
    """Check if the client is rate limited based on the client ID."""
    current_time = time()

    # Check if the client_id exists in the request_counts
    if client_id in request_counts:
        last_request_time, request_count = request_counts[client_id]['last_request_time'], request_counts[client_id]['count']

        # Check if the time window has passed
        if current_time - last_request_time > TIME_WINDOW:
            # Reset the count and timestamp if time window has passed
            request_counts[client_id] = {'count': 1, 'last_request_time': current_time}
            return False
        else:
            # If within time window, check if request count exceeds limit
            if request_count < RATE_LIMIT:
                # Increment the count and update the timestamp
                request_counts[client_id]['count'] += 1
                return False
            else:
                return True
    else:
        # If client_id doesn't exist, create a new entry
        request_counts[client_id] = {'count': 1, 'last_request_time': current_time}
        return False

def rate_limit_dependency(request: Request, auth_result: str = Depends(auth.verify)):
    """Rate limit logic using Depends to inject the request and user_id."""
    if auth_result.get("sub") is None or auth_result.get("sub") == "":
        raise HTTPException(status_code=401, detail="Unauthorized")
    # Generate the client ID based on client IP and user ID
    client_id = get_client_id(request, auth_result.get("sub"))

    # Check if the client is rate limited
    if is_rate_limited(client_id):
        raise HTTPException(status_code=429, detail="Rate limit exceeded. Try again later.")


# Endpoint to upload a resume and job description
@app.post("/api/upload-resume")
async def upload_resume(
    auth_result: str = Security(auth.verify),
    resume: UploadFile = File(...),
    job_description: str = Body(...),
    save_resume: bool = Body(...),
    rate_limit = Depends(rate_limit_dependency)
):
    resume_service = ResumeService()
    resume_content = await resume_service.extract_pdf_content(resume)
    # Call OpenAI API to get the ATS score
    try:
        resume_uuid = uuid.uuid4()
        # Save resume to database if requested
        if save_resume:

            content_hash = hashlib.sha256(resume_content.encode()).hexdigest() 
            existingresume = await resume_service.get_resume_by_hash(content_hash)
            if existingresume:
                logging.info(f"Resume already exists in the database: {existingresume.id}")
                resume_uuid = existingresume.id
            else:
                await resume_service.save_resume_to_db(resume_content, resume_uuid, auth_result.get("sub"),content_hash)
        # # Get enhanced ATS score analysis
        ats_analysis = await resume_service.get_enhanced_ats_score(resume_content, job_description)
        # Handle both enhanced and legacy format for activity logging
        ats_score_for_activity = ats_analysis.get("overall_score") or ats_analysis.get("score_breakdown", {}).get("final_ats_score")
        await ActivityService().add_activity(ActivityBase(type="resume-scanned", title=ats_analysis.get("title"), details={"atsScore": ats_score_for_activity,"data": ats_analysis}),auth_result.get("sub"))

        if save_resume:
            await resume_service.save_ats_to_db(job_description, resume_uuid, ats_analysis)

        # # Cache the resume data in Redis
        resume_service.cache_resume_data(str(resume_uuid), resume_content, job_description, ats_analysis)

        # Return the complete enhanced response (frontend will handle format detection)
        response_data = {
            "resume_id": str(resume_uuid),
            "resume_authenticity": ats_analysis.get("resume_authenticity"),
            "title": ats_analysis.get("title")
        }

        # Add all enhanced format fields if they exist
        if "overall_score" in ats_analysis:
            # Enhanced format - return the complete response
            response_data.update(ats_analysis)
        else:
            # Legacy format - maintain backward compatibility
            response_data["data"] = ats_analysis.get("score_breakdown")

        return response_data


    except Exception as e:
         # Handle edge cases
        if type(e) == openai.LengthFinishReasonError:
            logging.error(f"the uploaded content is outside the context window: {str(e)}")
            # Retry with a higher max tokens
            raise HTTPException(status_code=403, detail=f"The content is large")
        logging.error(f"Failed to generate ats score: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate ats score")

@app.delete("/api/delete-resume/{resume_id}")
async def delete_resume(
    resume_id: str,
    auth_result: str = Security(auth.verify),
    rate_limit = Depends(rate_limit_dependency)
):
    try:
        resumeservice = ResumeService()
        # Delete resume from database
        await resumeservice.delete_resume_from_db(resume_id, auth_result.get("sub"))
        return {"message": "Resume deleted successfully"}
    except Exception as e:
        logging.error(f"Failed to delete resume: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete resume")

@app.post("/api/tailor-resume-with-ats/{resume_id}")
async def tailor_resume_with_ats_comparison(
    resume_id: str,
    auth_result: str = Security(auth.verify),
    rate_limit = Depends(rate_limit_dependency)
):
    """
    Tailor resume and provide before/after ATS score comparison
    """
    try:

        # Get original resume metadata from Redis
        resume_metadata = redis_client.get(resume_id)
        if not resume_metadata:
            raise HTTPException(status_code=404, detail="Resume not found")

        resume_metadata_json = json.loads(resume_metadata)
        resume_content = resume_metadata_json.get("resume_content")
        original_ats_feedback = resume_metadata_json.get("resume_ats_feedback")
        job_description = resume_metadata_json.get("job_description")

        if resume_content is None or original_ats_feedback is None:
            raise HTTPException(status_code=400, detail="Resume data is missing")

        # Step 1: Generate tailored resume (reuse existing logic)
        tailored_cache_key = f"tailored_resume:{resume_id}"
        tailored_cached = redis_client.get(tailored_cache_key)

        if not tailored_cached:
            # Generate tailored resume using existing logic
            response = client.beta.chat.completions.parse(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT_TRAILOR_RESUME},
                    {
                        "role": "user",
                        "content": f"Job Description:\n<jd>{job_description}</jd>\n\nResume:\n<resume>{resume_content}</resume>\n\nATS Feedback:\n<ats-feedback>{original_ats_feedback}</ats-feedback>"
                    }
                ],
                response_format=ATSTailoredResumeTemplate,
            )

            tailored_resume_response = response.choices[0].message
            if not tailored_resume_response.parsed:
                if tailored_resume_response.refusal:
                    return {"refusal_message": tailored_resume_response.refusal}
                raise HTTPException(status_code=500, detail="Failed to generate tailored resume")

            tailored_resume_data = tailored_resume_response.parsed.dict()

            # Cache tailored resume
            redis_client.set(
                tailored_cache_key,
                json.dumps({"parsed_response": tailored_resume_data}),
                ex=86400
            )
        else:
            tailored_resume_data = json.loads(tailored_cached)["parsed_response"]

        # Step 2: Convert tailored resume back to text for ATS analysis
        resume_service = ResumeService()
        tailored_resume_text = resume_service.convert_structured_resume_to_text(tailored_resume_data)

        # Step 3: Get ATS score for tailored resume using the converted text
        # Note: This uses the structured data converted back to text, which may have some formatting differences
        # but preserves all the tailored content and improvements made by the AI
        tailored_ats_analysis = await resume_service.get_enhanced_ats_score(tailored_resume_text, job_description)

        # Step 4: Enforce score guarantee - ensure tailored scores are never lower than original scores
        tailored_ats_analysis = resume_service.enforce_score_guarantee(original_ats_feedback, tailored_ats_analysis)

        # Step 5: Prepare comparison response
        comparison_response = {
            "tailored_resume": tailored_resume_data,
            "original_ats_score": original_ats_feedback,
            "tailored_ats_score": tailored_ats_analysis,
            "improvement_summary": {
                "score_improvement": tailored_ats_analysis.get("overall_score", 0) - original_ats_feedback.get("overall_score", 0),
                "original_score": original_ats_feedback.get("overall_score", 0),
                "tailored_score": tailored_ats_analysis.get("overall_score", 0)
            }
        }

        # Log activity for tailored resume
        await ActivityService().add_activity(
            ActivityBase(
                type="resume-tailored",
                title=f"Resume Tailored with ATS Comparison - {tailored_ats_analysis.get('job_title', 'Job')}",
                details={
                    "data": comparison_response
                }
            ),
            auth_result.get("sub")
        )

        logging.info(f"Tailored resume with ATS comparison generated for resume_id: {resume_id}")
        return comparison_response

    except Exception as e:
        # Handle edge cases
        if type(e) == openai.LengthFinishReasonError:
            raise HTTPException(status_code=403, detail="The content is too large")
        logging.error(f"Failed to tailor resume with ATS comparison: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to tailor resume with ATS comparison: {str(e)}")

@app.post("/api/trailor-resume/{resume_id}")
async def tailor_resume(
    resume_id: str,
    auth_result: str = Security(auth.verify),
    rate_limit = Depends(rate_limit_dependency)
):
    try:
        # Check if tailored resume is already cached in Redis
        cache_key = f"tailored_resume:{resume_id}"
        cached_response = redis_client.get(cache_key)

        if cached_response:
            logging.info(f"Tailored resume retrieved from cache for resume_id: {resume_id}")
            return json.loads(cached_response)

        # If not in cache, retrieve the resume content from Redis
        resume_metadata = redis_client.get(resume_id)
        if not resume_metadata:
            raise HTTPException(status_code=404, detail="Resume not found")
            
        resume_metadata_json = json.loads(resume_metadata)
        resume_content = resume_metadata_json.get("resume_content")
        resume_ats_feedback = resume_metadata_json.get("resume_ats_feedback")
        job_description = resume_metadata_json.get("job_description")
        
        if resume_content is None or resume_ats_feedback is None:
            raise HTTPException(status_code=400, detail="Resume data is missing")
        
    except Exception as e:
        logging.error(f"Failed to retrieve resume: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve resume")
    
    # Call OpenAI API to tailor resume
    try:
        response = client.beta.chat.completions.parse(
            model="gpt-4o-mini-2024-07-18",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT_TRAILOR_RESUME},
                {
                    "role": "user",
                    "content": f"Job Description:\n<jd>{job_description}</jd>\n\nResume:\n<resume>{resume_content}</resume>\n\nATS Feedback:\n<ats-feedback>{resume_ats_feedback}</ats-feedback>"
                }
            ],
            response_format=ATSTailoredResumeTemplate,
        )
        ats_trailored_resume = response.choices[0].message
        if ats_trailored_resume.parsed:
            parsed_response = ats_trailored_resume.parsed.dict()
            await ActivityService().add_activity(
                ActivityBase(
                    type="resume-tailored", 
                    title=parsed_response.get("activity_title"), 
                    details={"data": parsed_response}
                ),
                auth_result.get("sub")
            )
            
            # Cache the tailored resume in Redis (expires in 24 hours)
            response_data = {"parsed_response": parsed_response}
            redis_client.set(
                cache_key, 
                json.dumps(response_data),
                ex=86400
            )
            
            logging.info(f"Tailored resume cached for resume_id: {resume_id}")
            return response_data
            
        elif ats_trailored_resume.refusal:
            return {"refusal_message": ats_trailored_resume.refusal}
            
    except Exception as e:
        # Handle edge cases
        if type(e) == openai.LengthFinishReasonError:
            # Retry with a higher max tokens
            raise HTTPException(status_code=403, detail=f"The content is large")
        logging.error(f"Failed to tailor resume: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to tailor your resume: {str(e)}")

# Endpoint to generate cover letter
@app.post("/api/generate-cover-letter")
async def generate_cover_letter(
    auth_result: str = Security(auth.verify),
    resume: UploadFile = File(...),
    job_description: str = Body(...),
    tone: str = Body(...),
    rate_limit = Depends(rate_limit_dependency)
):
    # Validate file type
    if resume.content_type != "application/pdf":
        raise HTTPException(status_code=400, detail="Only PDF files are allowed.")

    resume_content = ""
    # Extract content from the PDF file using PyPDF
    try:
        pdf_reader = PdfReader(io.BytesIO(await resume.read()))
        resume_content = ""
        for page in pdf_reader.pages:
            resume_content += page.extract_text()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read PDF: {str(e)}")

    # Call OpenAI API to generate cover letter
    try:
        response = client.beta.chat.completions.parse(
            model="gpt-4o-mini-2024-07-18",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT_COVER_LETTER_BUILDER},
                {
                    "role": "user",
                    "content": f"Job Description:\n{job_description}\n\nResume:\n{resume_content}\n\nTone:{tone}"
                }
            ],
             response_format=CoverLetterTemplate,
        )
        cover_letter = response.choices[0].message
        if cover_letter.parsed:
            parsed_response = cover_letter.parsed.dict()
            await ActivityService().add_activity(ActivityBase(type="cover-letter", title=parsed_response.get("title"), details={"data": parsed_response}),auth_result.get("sub"))    
            logging.info(f"Cover letter response from LLM: {json.dumps(parsed_response)}")


            return {
                "data": parsed_response,
            }
            
        elif cover_letter.refusal:
            # handle refusal
            {"refusal_message": cover_letter.refusal}
    except Exception as e:
         # Handle edge cases
        if type(e) == openai.LengthFinishReasonError:
            logging.error(f"the uploaded content is outside the context window: {str(e)}")
            # Retry with a higher max tokens
            raise HTTPException(status_code=403, detail=f"The content is large")
        logging.error(f"Failed to generated cover letter: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generated ats score")

class JobSearchQuery(BaseModel):
    query: str

@app.post("/api/job-search")
async def job_search(
    auth_result: str = Security(auth.verify),
    query: JobSearchQuery = Body(...),
    rate_limit=Depends(rate_limit_dependency)
):
    try:
        jobservice = JobService()
        search_queries = await jobservice.generate_search_queries(simple_query=query.query)
        if "refusal_message" in search_queries:
            return {"refusal_message": search_queries["refusal_message"]}
        job_results = await jobservice.search_jobs(search_queries.get("query"))
        final_results = await jobservice.summarize_job_results(job_results)
        return {"jobs": final_results}
    except Exception as e:
        logging.error(f"Failed to process job search: {str(e)}")
        raise HTTPException(status_code=500, detail="Error processing job search")

@app.get("/api/activities")
async def get_activities(
    mode: str = Query(default="recent", enum=["recent", "history"]),
    auth_result: str = Security(auth.verify),
):
    try:
        activities = await ActivityService().get_activities(
            user_id=auth_result.get("sub"),
            mode=mode
        )
        return {"activities": activities}
    except Exception as e:
        logging.error(f"Failed to retrieve activities: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve activities")

@app.get("/api/stats")
async def get_stats(
    auth_result: str = Security(auth.verify),
):
    try:
        stats = await ActivityService().get_stats(auth_result.get("sub"))
        return {"stats": stats}
    except Exception as e:
        logging.error(f"Failed to retrieve stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve stats")

@app.get("/test")
async def test(
    auth_result: str = Security(auth.verify),
):
            await ActivityService().add_activity(ActivityBase(type="resume-tailored", title="Resume Tailored for testing", details={}),auth_result.get("sub"))
            return {"message": "Test successful"}

@app.post("/api/deep-job-search")
async def deep_job_search(

    auth_result: str = Security(auth.verify),
    resume: UploadFile = File(...),
    rate_limit=Depends(rate_limit_dependency)
):
    try:
        jobservice = JobService()
        resume_content = await jobservice.extract_pdf_content(resume)
        
        search_queries = await jobservice.generate_search_queries(resume_content=resume_content)
        if "refusal_message" in search_queries:
            return {"refusal_message": search_queries["refusal_message"]}
            
        job_results = await jobservice.search_jobs(search_queries.get("query"))
        
        final_results = await jobservice.summarize_job_results(job_results)
        
        return {"jobs": final_results}
    except Exception as e:
        logging.error(f"Failed to process job search: {str(e)}")
        raise HTTPException(status_code=500, detail="Error processing job search")

@app.post("/api/review-resume")
async def review_resume(
    auth_result: str = Security(auth.verify),
    resume: UploadFile = File(...),
    save_resume: bool = Body(...),
    rate_limit = Depends(rate_limit_dependency)
):
    resume_service = ResumeService()
    resume_content = await resume_service.extract_pdf_content(resume)
    
    try:
        # Generate content hash for the resume
        content_hash = hashlib.sha256(resume_content.encode()).hexdigest()
        
        # Use content hash for cache key instead of UUID
        cache_key = f"resume_review:{content_hash}"
        cached_response = redis_client.get(cache_key)
        
        if cached_response:
            logging.info(f"Resume review retrieved from cache for content_hash: {content_hash}")
            return json.loads(cached_response)
        
        # Generate UUID for new resumes
        resume_uuid = str(uuid.uuid4())
            
        # Save resume to database if requested
        if save_resume:
            existing_resume = await resume_service.get_resume_by_hash(content_hash)
            if existing_resume:
                logging.info(f"Resume already exists in the database: {existing_resume.id}")
                resume_uuid = str(existing_resume.id)
            else:
                await resume_service.save_resume_to_db(resume_content, resume_uuid, auth_result.get("sub"), content_hash)
        
        # Get general resume analysis
        resume_analysis = await resume_service.get_resume_review(resume_content)
        
        await ActivityService().add_activity(
            ActivityBase(
                type="resume-reviewed", 
                title=resume_analysis.get("title"), 
                details={"data": resume_analysis}
            ),
            auth_result.get("sub")
        )
        
        # Cache the resume review in Redis (expires in 24 hours)
        response_data = {
            "resume_id": resume_uuid,
            "data": resume_analysis
        }
        redis_client.set(
            cache_key, 
            json.dumps(response_data),
            ex=86400
        )
        
        logging.info(f"Resume review cached with content_hash: {content_hash}")
        
        return response_data
        
    except Exception as e:
        # Handle edge cases
        if type(e) == openai.LengthFinishReasonError:
            logging.error(f"The uploaded content is outside the context window: {str(e)}")
            raise HTTPException(status_code=403, detail="The content is too large")
        
        logging.error(f"Failed to review resume: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to review resume")

@app.get("/api/activities/{id}")
async def get_activity_by_id(
    id: str,
    auth_result: str = Security(auth.verify),
):
    try:
        activity = await ActivityService().get_activity_by_id(id, auth_result.get("sub"))
        if not activity:
            raise HTTPException(status_code=404, detail="Activity not found")
        return activity
    except Exception as e:
        logging.error(f"Failed to retrieve activity: {str(e)}") 
        raise HTTPException(status_code=500, detail="Failed to retrieve activity")