from datetime import datetime
import os
import logging
import io
from fastapi import HTTPException, UploadFile
from typing import Optional
from PyPDF2 import PdfReader
import openai
from services.serve import client
from utils import search_duckduckgo, search_serper
from search_agent import summarize_jobs
from pydantic import BaseModel
from prompts import SYSTEM_PROMPT_JOB_SEARCH, SYSTEM_PROMPT_JOB_SEARCH_FROM_QUERY

class JobSearchQueries(BaseModel):
    query: str

class JobService:
    def __init__(self):
        self.openai_client = client

    async def extract_pdf_content(self, resume_file: UploadFile) -> str:

        if resume_file.content_type != "application/pdf":
            raise HTTPException(status_code=400, detail="Only PDF files are allowed.")

        resume_content = ""
        try:
            pdf_reader = PdfReader(io.BytesIO(await resume_file.read()))
            for page in pdf_reader.pages:
                resume_content += page.extract_text() + "\n"
            return resume_content
        except Exception as e:
            logging.error(f"Failed to read PDF: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to read PDF: {str(e)}")

    async def generate_search_queries(
        self, 
        resume_content: Optional[str] = None, 
        simple_query: Optional[str] = None
    ) -> dict:
        try:
            today_date_str = datetime.today().strftime("%B %d, %Y")
            logging.info(f"Today's date: {today_date_str}")

            # Validate input
            if not resume_content and not simple_query:
                raise HTTPException(status_code=400, detail="Either resume_content or simple_query must be provided.")

            # Select appropriate system prompt and user message
            if resume_content:
                system_prompt = SYSTEM_PROMPT_JOB_SEARCH
                user_message = f"<resume>{resume_content}</resume>\n<today-date>{today_date_str}</today-date>"
            else:
                system_prompt = SYSTEM_PROMPT_JOB_SEARCH_FROM_QUERY
                user_message = f"<query>{simple_query}</query>\n<today-date>{today_date_str}</today-date>"

            # Call OpenAI model
            response = self.openai_client.beta.chat.completions.parse(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                response_format=JobSearchQueries
            )

            search_queries = response.choices[0].message

            if search_queries.parsed:
                parsed_response = search_queries.parsed.dict()
                logging.info(f"Generated search queries: {parsed_response}")
                return parsed_response
            elif search_queries.refusal:
                return {"refusal_message": search_queries.refusal}

            return {}

        except Exception as e:
            if isinstance(e, openai.LengthFinishReasonError):
                logging.error(f"The uploaded content is outside the context window: {str(e)}")
                raise HTTPException(status_code=403, detail="The content is too large")
            
            logging.error(f"Failed to generate search queries: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to generate search queries")

         
    # async def generate_search_queries(self, resume_content: str) -> dict:
    #     try:
    #         logging.info(f"today date:{datetime.today().strftime('%B %d, %Y')}")
    #         response = self.openai_client.beta.chat.completions.parse(
    #             model="gpt-4o-mini-2024-07-18",
    #             messages=[
    #                 {"role": "system", "content": SYSTEM_PROMPT_JOB_SEARCH},
    #                 {"role": "user", "content": f"""<resume>{resume_content}</resume>\n<today-date>{datetime.today().strftime("%B %d, %Y")}</today-date>"""}
    #             ],
    #             response_format=JobSearchQueries
    #         )
            
    #         search_queries = response.choices[0].message
    #         if search_queries.parsed:
    #             parsed_response = search_queries.parsed.dict()
    #             logging.info(f"Generated search queries: {parsed_response}")
    #             return parsed_response
    #         elif search_queries.refusal:
    #             return {"refusal_message": search_queries.refusal}
            
    #         return {}
    #     except Exception as e:
    #         if isinstance(e, openai.LengthFinishReasonError):
    #             logging.error(f"the uploaded content is outside the context window: {str(e)}")
    #             # Retry with a higher max tokens
    #             raise HTTPException(status_code=403, detail=f"The content is large")
    #         logging.error(f"Failed to generate search queries: {str(e)}")
    #         raise HTTPException(status_code=500, detail="Failed to generate search queries")

    async def search_jobs(self, query: str) -> list:
        try:
            job_results = []
            api_key = os.getenv("SERPER_API_KEY")
            search_results = search_serper(query,api_key)
            if len(search_results) > 0:
                job_results.extend(search_results)
            logging.info(f"query: {query}")
            logging.info(f"Found {len(job_results)} job results")
            return job_results
        except Exception as e:
            logging.error(f"Failed to search jobs: {str(e)}")
            raise HTTPException(status_code=500, detail="Error searching for jobs")

    async def summarize_job_results(self, job_results: list) -> list:
        try:
            final_results = summarize_jobs(job_results)
            return final_results
        except Exception as e:
            logging.error(f"Failed to summarize jobs: {str(e)}")
            raise HTTPException(status_code=500, detail="Error summarizing job postings")
            
