from services.serve import client_db_engine, client, redis_client
from sqlalchemy.orm import sessionmaker
import logging
from models.activities import Activity  , ActivityBase
from models.resume import ResumeModel,AtsModel
from collections import Counter
import json
from datetime import datetime, date, timedelta
import calendar
from sqlalchemy import desc


class ActivityService:
    def __init__(self):
        Session = sessionmaker(bind=client_db_engine)
        self.db_session = Session()
        self.openai_client = client
        self.redis_client = redis_client

    async def get_stats(self, user_id: str):
        cache_key = f"user:{user_id}:stats"
        try:
            # Try fetching from Redis cache
            cached_stats = self.redis_client.get(cache_key)
            if cached_stats:
                logging.info("Stats retrieved from cache")
                return json.loads(cached_stats)

            with self.db_session as session:
                activities = session.query(Activity).filter(Activity.user_id == user_id).order_by(desc(Activity.date)).all()
                activity_counts = Counter(activity.type for activity in activities)

                ats_score = next(
                    (activity.details.get("atsScore") for activity in activities if activity.type == "resume-scanned" and activity.details),
                    0
                )

                stats = {
                    "atsScore": ats_score,
                    "jobsMatched": activity_counts.get("job-applied", 0),
                    "tailored": activity_counts.get("resume-tailored", 0),
                    "coverLetters": activity_counts.get("cover-letter", 0)
                }

                # Cache the result in Redis with longer expiration (24 hours)
                self.redis_client.set(cache_key, json.dumps(stats), ex=86400)

                return stats
        except Exception as e:
            raise e


    async def add_activity(self, activity: ActivityBase, user_id: str):
        try:
            with self.db_session as session:
                new_activity = Activity(
                    user_id=user_id,
                    type=activity.type,
                    title=activity.title,
                    details=activity.details if activity.details else {}
                )
                session.add(new_activity)
                session.commit()
                session.refresh(new_activity)
                
                # Invalidate cache when new activity is added
                cache_key = f"user:{user_id}:stats"
                self.redis_client.delete(cache_key)
                
                # Also invalidate activities cache
                activities_cache_key = f"user:{user_id}:activities:history"
                recent_cache_key = f"user:{user_id}:activities:recent"
                self.redis_client.delete(activities_cache_key)
                self.redis_client.delete(recent_cache_key)
                
                return new_activity
        except Exception as e:
            raise e

    async def get_activities(self, user_id: str, mode: str ):
        cache_key = f"user:{user_id}:activities:{mode}"

        try:
            cached_activities = self.redis_client.get(cache_key)
            if cached_activities:
                logging.info(f"Activities retrieved from cache ({mode})")
                return json.loads(cached_activities)

            with self.db_session as session:
                query = session.query(Activity).filter_by(user_id=user_id)

                if mode == "history":
                    # Filter for current month
                    today = date.today()
                    # first_day = today.replace(day=1)
                    # last_day = today.replace(
                    #     day=calendar.monthrange(today.year, today.month)[1]
                    # )
                    # query = query.filter(Activity.date >= first_day, Activity.date <= last_day).order_by(Activity.date.desc())

                    thirty_days_ago = today - timedelta(days=30)
                    query = query.filter(Activity.date >= thirty_days_ago).order_by(Activity.date.desc())
                else:
                    # Limit to 3 recent activities
                    query = query.order_by(Activity.date.desc()).limit(5)

                activities = query.all()

                result = [{
                    "id": activity.id,
                    "type": activity.type,
                    "title": activity.title,
                    "date": activity.date.isoformat(),
                    "details": activity.details,
                    "activity_id":str(activity.activity_id)
                } for activity in activities]

                # Cache for 1 minute
                self.redis_client.set(cache_key,  json.dumps(result),ex=60)

                return result

        except Exception as e:
            raise e

    async def get_activity_by_id(self, activity_id: int, user_id: str):
        try:
            with self.db_session as session:
                activity = session.query(Activity).filter_by(activity_id=activity_id, user_id=user_id).first()
                if not activity:
                    return None
                return {
                    "id": activity.id,
                    "type": activity.type,
                    "title": activity.title,
                    "date": activity.date.isoformat(),
                    "details": activity.details
                }
        except Exception as e:
            raise e