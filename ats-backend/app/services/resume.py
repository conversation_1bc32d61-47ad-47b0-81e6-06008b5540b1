import json
from datetime import datetime
import logging
import hashlib
from prompts import SYSTEM_PROMPT_SKILLS_WEIGHTAGE, UPLOAD_SAVE_RESUME_PROMPT, SYSTEM_PROMPT_RESUME_REVIEW, ENHANCED_SYSTEM_PROMPT_ATS_SCORE
import io
from services.serve import client_db_engine, client, redis_client
from fastapi import HTTPException
from sqlalchemy.orm import sessionmaker
from models.resume import ResumeModel, ResumeParseResponse, ATSScoreResponse, AtsModel, ResumeReviewResponse, EnhancedATSScoreResponse
from PyPDF2 import PdfReader
from utils import calculate_ats_score, search_duckduckgo
import openai
from models.resume import AtsModel
import fitz  # PyMuPDF
from collections import defaultdict
import statistics


class ResumeService:
    def __init__(self):
        Session= sessionmaker(bind=client_db_engine)
        self.db_session= Session()
        self.openai_client= client
        self.redis_client= redis_client

    async def extract_pdf_content(self, resume):
        # Validate file type
        if resume.content_type != "application/pdf":
            raise HTTPException(status_code=400, detail="Only PDF files are allowed.")
        
        try:
            # Read the file into memory
            pdf_bytes = await resume.read()
            
            # Get the filename
            filename = resume.filename
            
            # Open PDF with PyMuPDF
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            
            # Extract text
            resume_content = ""
            fonts = defaultdict(set)
            font_sizes = []
            left_margins = []
            links = []
            
            # Process each page
            for page_num, page in enumerate(doc):
                # Extract text
                resume_content += page.get_text() + "\n"
                
                # Extract links
                for link in page.get_links():
                    if link.get("uri"):
                        links.append(link["uri"])
                
                # Extract visual metadata
                blocks = page.get_text("dict")["blocks"]
                for block in blocks:
                    for line in block.get("lines", []):
                        for span in line.get("spans", []):
                            # Font and size
                            fonts[span["font"]].add(span["size"])
                            font_sizes.append(round(span["size"], 1))
                            
                            # Left margin (x0)
                            left_margins.append(round(span["bbox"][0], 1))
            
            # Add filename as metadata
            resume_content = f"<filename>{filename}</filename>\n\n" + resume_content
            
            # Add links as metadata for the LLM to process
            if links:
                resume_content += "\n\n<links>\n" + "\n".join(links) + "\n</links>"
            
            # Process visual metadata
            visual_metadata = None
            if font_sizes and left_margins:
                # Basic layout analysis
                num_fonts = len(fonts)
                num_font_sizes = len(set(font_sizes))
                font_consistent = num_fonts <= 3 and num_font_sizes <= 5  # Allow some variation
                
                # Page dimensions for margin calculation from the first page
                page_width = doc[0].rect.width if doc.page_count > 0 else 0
                
                # Check alignment consistency
                std_dev_margin = statistics.stdev(left_margins) if len(left_margins) > 1 else 0
                alignment_irregular = std_dev_margin > 10  # more than 10px variance is suspicious
                
                # Analyze margins
                left_margin_avg = statistics.mean(left_margins) if left_margins else 0
                margin_balanced = 20 <= left_margin_avg <= 80  # reasonable margin range
                
                visual_metadata = {
                    "font_consistency": font_consistent,
                    "fonts_count": num_fonts,
                    "font_sizes_count": num_font_sizes,
                    "margin_variance": round(std_dev_margin, 2),
                    "alignment_consistent": not alignment_irregular,
                    "balanced_margins": margin_balanced,
                    "page_count": doc.page_count
                }
            
            # Add visual metadata to the result
            if visual_metadata:
                resume_content += "\n\n<visual-metadata>\n" + json.dumps(visual_metadata) + "\n</visual-metadata>"
            
            # Close the document
            doc.close()
            
            logging.info(f"Resume content extracted successfully: {resume_content[:100]}\n...\n" + f"{resume_content[-500:]}\n\n")  # Log just the start  and end for brevity
            return resume_content
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to read PDF: {str(e)}")

    async def save_ats_to_db(self,job_description:str,resume_id:str,ats_analysis:dict):
        try:
            with self.db_session as session:
                existing_entry = session.query(AtsModel).filter_by(resume_id=resume_id ).first()
                if existing_entry:
                    existing_entry.job_description = job_description
                    existing_entry.ats = ats_analysis
                    session.commit()
                    return
                
                ats_entry = AtsModel(
                    resume_id=resume_id,
                    job_description=job_description,
                    ats=ats_analysis
                )
                session.add(ats_entry)
                session.commit()
        except Exception as e:
            raise e

    async def get_resume_review(self, resume_content: str) -> dict:
        """Review resume content without comparing to a job description"""
        try:
            response = self.openai_client.beta.chat.completions.parse(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT_RESUME_REVIEW},
                    {"role": "user", "content": f"Resume:\n<resume>{resume_content}</resume><today-date>{datetime.today().strftime('%B %d, %Y')}</today-date>"}
                ],
                response_format=ResumeReviewResponse,
            )
            
            review_result = response.choices[0].message
            
            if review_result.parsed:
                parsed_response = review_result.parsed.dict()
                logging.info(f"Resume review response from LLM: {json.dumps(parsed_response)}")

                # Calculate overall score using AI-provided weights
                overall_score = self.calculate_overall_score(parsed_response)
                parsed_response["overall_score"] = overall_score

                # Return the complete enhanced format
                return parsed_response
            elif review_result.refusal:
                return {"refusal_message": review_result.refusal}
            else:
                logging.error(f"Resume review response from LLM: {review_result}")
                raise Exception("No valid response from resume review model")
                    
        except openai.LengthFinishReasonError as e:
            logging.error(f"Failed to generate resume review: {str(e)}")
            raise Exception("Failed to generate resume review: Response length exceeded")
        except Exception as e:
            logging.error(f"Failed to generate resume review: {str(e)}")
            raise Exception("Failed to generate resume review")

    def calculate_overall_score(self, parsed_response: dict) -> int:
        """Calculate overall score using AI-provided section weights"""
        try:
            # Extract section weights from AI response
            section_weights = parsed_response.get("section_weights", [])

            # Define section mapping
            section_mapping = {
                "ats_compatibility": "ats_compatibility",
                "work_experience": "work_experience",
                "projects": "projects",
                "skills": "skills_summary",
                "formatting": "formatting_and_layout",
                "language_grammar": "language_grammar_tone",
                "header_contact": "header_and_contact_info",
                "education": "education",
                "achievements": "achievements",
                "certifications": "certifications"
            }

            # Calculate weighted score
            total_weighted_score = 0
            total_weight = 0

            for weight_info in section_weights:
                section_name = weight_info.get("section", "")
                weight = weight_info.get("weight", 0) / 100  # Convert percentage to decimal

                # Map to actual section name in response
                actual_section = section_mapping.get(section_name.lower().replace(" ", "_").replace("/", "_"))
                if actual_section and actual_section in parsed_response:
                    section_data = parsed_response[actual_section]
                    if isinstance(section_data, dict) and "score" in section_data:
                        section_score = section_data["score"]
                        weighted_score = section_score * weight
                        total_weighted_score += weighted_score
                        total_weight += weight

                        logging.info(f"Section: {actual_section}, Score: {section_score}, Weight: {weight*100}%, Weighted: {weighted_score}")

            # Calculate final score
            if total_weight > 0:
                overall_score = round(total_weighted_score / total_weight * (total_weight))
                overall_score = max(0, min(100, overall_score))  # Ensure 0-100 range
            else:
                # Fallback to simple average if weights are missing
                logging.warning("No valid weights found, using simple average")
                overall_score = self.calculate_simple_average_score(parsed_response)

            logging.info(f"Calculated overall score: {overall_score} (total_weight: {total_weight})")
            return overall_score

        except Exception as e:
            logging.error(f"Error calculating overall score: {str(e)}")
            # Fallback to simple average
            return self.calculate_simple_average_score(parsed_response)

    def calculate_simple_average_score(self, parsed_response: dict) -> int:
        """Fallback method to calculate simple average of all section scores"""
        try:
            section_names = [
                "formatting_and_layout", "header_and_contact_info", "skills_summary",
                "work_experience", "education", "certifications", "projects",
                "achievements", "language_grammar_tone", "ats_compatibility"
            ]

            total_score = 0
            valid_sections = 0

            for section in section_names:
                if section in parsed_response:
                    section_data = parsed_response[section]
                    if isinstance(section_data, dict) and "score" in section_data:
                        total_score += section_data["score"]
                        valid_sections += 1

            if valid_sections > 0:
                return round(total_score / valid_sections)
            else:
                return 50  # Default fallback score

        except Exception as e:
            logging.error(f"Error in simple average calculation: {str(e)}")
            return 50

    def cache_resume_review(self, resume_uuid: str, resume_content: str, review_analysis: dict) -> None:
        """Cache resume review data in Redis"""
        try:
            self.redis_client.set(
                f"review:{resume_uuid}", 
                json.dumps({
                    "resume_content": resume_content,
                    "resume_review": review_analysis,
                }), 
                ex=3600  
            )
        except Exception as e:
            logging.error(f"Failed to cache resume review data: {str(e)}")
            # Non-critical error, continue processing

    def cache_resume_data(self, resume_uuid: str, resume_content: str, job_description: str, ats_feedback: dict) -> None:
        """Cache resume data in Redis"""
        try:
            self.redis_client.set(
                resume_uuid, 
                json.dumps({
                    "resume_content": resume_content,
                    "resume_ats_feedback": ats_feedback,
                    "job_description": job_description
                }), 
                ex=3600  # Cache eviction time set to 1 hour
            )
        except Exception as e:
            logging.error(f"Failed to cache resume data: {str(e)}")
            # Non-critical error, continue processing

    async def parse_resume(self, resume_content: str) -> dict:
        """Parse resume content using LLM"""
        try:
            response = self.openai_client.beta.chat.completions.parse(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {"role": "system", "content": UPLOAD_SAVE_RESUME_PROMPT},
                    {"role": "user", "content": f"Resume content:\n{resume_content}"}
                ],
                response_format=ResumeParseResponse,
            )
            
            parsed_info = response.choices[0].message
            
            if parsed_info.parsed:
                return parsed_info.parsed.dict()
            elif parsed_info.refusal:
                return {"refusal_message": parsed_info.refusal}
            else:
                raise Exception("No valid response from resume parser")
                
        except Exception as e:
            raise Exception(f"Failed to parse resume: {str(e)}")

    async def get_enhanced_ats_score(self, resume_content: str, job_description: str) -> dict:
        """Get enhanced ATS score with detailed feedback and actionable improvements"""
        try:
            response = self.openai_client.beta.chat.completions.parse(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {"role": "system", "content": ENHANCED_SYSTEM_PROMPT_ATS_SCORE},
                    {
                        "role": "user",
                        "content": f"Job Description:\n<job-description>{job_description}</job-description>\n\nResume:\n<resume>{resume_content}</resume><today-date>{datetime.today().strftime('%B %d, %Y')}</today-date>"
                    }
                ],
                response_format=EnhancedATSScoreResponse,
            )

            ats_score = response.choices[0].message

            if ats_score.parsed:
                parsed_response = ats_score.parsed.dict()
                logging.info(f"Enhanced ATS response from LLM: {json.dumps(parsed_response)}")

                # Calculate overall score using section scores and weights
                overall_score = self.calculate_ats_overall_score(parsed_response)
                parsed_response["overall_score"] = overall_score

                return parsed_response
            elif ats_score.refusal:
                return {"refusal_message": ats_score.refusal}
            else:
                logging.error(f"Enhanced ATS response from LLM: {ats_score}")
                raise Exception("No valid response from enhanced ATS model")

        except Exception as e:
            logging.error(f"Failed to get enhanced ATS score: {str(e)}")
            raise Exception(f"Failed to get enhanced ATS score: {str(e)}")

    def calculate_ats_overall_score(self, parsed_response: dict) -> int:
        """Calculate overall ATS score using section scores and their weights"""
        try:
            # Define section mapping and extract scores
            sections = [
                "experience_level_match", "skills_match", "education_match",
                "job_specific_keywords", "achievements_responsibilities",
                "industry_relevance", "certifications_training"
            ]

            total_weighted_score = 0
            total_weight = 0

            for section in sections:
                if section in parsed_response:
                    section_data = parsed_response[section]
                    if isinstance(section_data, dict) and "score" in section_data and "weightage" in section_data:
                        score = section_data["score"]
                        weight = section_data["weightage"] / 100  # Convert percentage to decimal

                        weighted_score = score * weight
                        total_weighted_score += weighted_score
                        total_weight += weight

                        logging.info(f"ATS Section: {section}, Score: {score}, Weight: {weight*100}%, Weighted: {weighted_score}")

            # Calculate final score
            if total_weight > 0:
                overall_score = round(total_weighted_score)
                overall_score = max(0, min(100, overall_score))  # Ensure 0-100 range
            else:
                # Fallback to simple average if weights are missing
                logging.warning("No valid ATS weights found, using simple average")
                overall_score = self.calculate_ats_simple_average(parsed_response)

            logging.info(f"Calculated ATS overall score: {overall_score} (total_weight: {total_weight})")
            return overall_score

        except Exception as e:
            logging.error(f"Error calculating ATS overall score: {str(e)}")
            # Fallback to simple average
            return self.calculate_ats_simple_average(parsed_response)

    def enforce_score_guarantee(self, original_ats_score: dict, tailored_ats_score: dict) -> dict:
        """
        Enforce the score guarantee: ensure tailored scores are never lower than original scores.
        If any tailored score is lower, set it equal to the original score.
        """
        try:
            # Create a copy of the tailored score to modify
            guaranteed_score = tailored_ats_score.copy()

            # List of section score fields to check
            section_fields = [
                'experience_level_match',
                'skills_match',
                'education_match',
                'job_specific_keywords',
                'achievements_responsibilities',
                'industry_relevance',
                'certifications_training'
            ]

            # Check and enforce guarantee for each section
            for field in section_fields:
                if field in original_ats_score and field in guaranteed_score:
                    original_section = original_ats_score[field]
                    tailored_section = guaranteed_score[field]

                    # Check if both sections have score field
                    if isinstance(original_section, dict) and isinstance(tailored_section, dict):
                        if 'score' in original_section and 'score' in tailored_section:
                            original_score = original_section['score']
                            tailored_score = tailored_section['score']

                            # If tailored score is lower, set it to original score
                            if tailored_score < original_score:
                                logging.warning(f"Tailored score for {field} ({tailored_score}) was lower than original ({original_score}). Setting to original score.")
                                guaranteed_score[field]['score'] = original_score
                                # Also update status if score was improved
                                if original_score >= 90:
                                    guaranteed_score[field]['status'] = 'excellent'
                                elif original_score >= 75:
                                    guaranteed_score[field]['status'] = 'good'
                                elif original_score >= 60:
                                    guaranteed_score[field]['status'] = 'needs_improvement'
                                else:
                                    guaranteed_score[field]['status'] = 'poor'

            # Check and enforce guarantee for overall score
            original_overall = original_ats_score.get('overall_score', 0)
            tailored_overall = guaranteed_score.get('overall_score', 0)

            if tailored_overall < original_overall:
                logging.warning(f"Tailored overall score ({tailored_overall}) was lower than original ({original_overall}). Setting to original score.")
                guaranteed_score['overall_score'] = original_overall

            # Check and enforce guarantee for job match percentage
            original_match = original_ats_score.get('job_match_percentage', 0)
            tailored_match = guaranteed_score.get('job_match_percentage', 0)

            if tailored_match < original_match:
                logging.warning(f"Tailored job match percentage ({tailored_match}) was lower than original ({original_match}). Setting to original percentage.")
                guaranteed_score['job_match_percentage'] = original_match

            logging.info("Score guarantee enforcement completed successfully")
            return guaranteed_score

        except Exception as e:
            logging.error(f"Error enforcing score guarantee: {str(e)}")
            # If there's an error, return the tailored score as-is
            return tailored_ats_score

    def calculate_ats_simple_average(self, parsed_response: dict) -> int:
        """Fallback method to calculate simple average of all ATS section scores"""
        try:
            sections = [
                "experience_level_match", "skills_match", "education_match",
                "job_specific_keywords", "achievements_responsibilities",
                "industry_relevance", "certifications_training"
            ]

            total_score = 0
            valid_sections = 0

            for section in sections:
                if section in parsed_response:
                    section_data = parsed_response[section]
                    if isinstance(section_data, dict) and "score" in section_data:
                        total_score += section_data["score"]
                        valid_sections += 1

            if valid_sections > 0:
                return round(total_score / valid_sections)
            else:
                return 50  # Default fallback score

        except Exception as e:
            logging.error(f"Error in ATS simple average calculation: {str(e)}")
            return 50

    async def get_resume_by_hash(self, content_hash: str) -> ResumeModel:
        """Get resume by content hash"""
        try:
            with self.db_session as session:
                resume = session.query(ResumeModel).filter_by(content_hash=content_hash).first()
                if not resume:
                    return None
                return resume
        except Exception as e:
            raise e

    async def save_resume_to_db(self, resume_content: str, resume_uuid: str, user_id: str,content_hash:str) -> None:
        """Parse resume and save to database"""
        try:
            content_hash = hashlib.sha256(resume_content.encode()).hexdigest()
            resume = await self.get_resume_by_hash(content_hash)
            if resume:
                logging.info(f"Resume already exists in the database: {resume.id}")
                return
            # Parse resume content
            parsed_info = await self.parse_resume(resume_content)
            logging.info(f"Resume parsed successfully: {type(parsed_info)}")
            
            if parsed_info.get("refusal_message"):
                logging.warning(f"Resume parsing refused: {parsed_info.get('refusal_message')}")
                return
                
            await self.create_resume(parsed_info, resume_uuid, user_id,content_hash)
            
        except Exception as e:
            logging.error(f"Failed to save resume to database: {str(e)}")
            # Log error but don't fail the whole request

    def _extract_text_from_linktext(self, field) -> str:
        """Helper to extract text from LinkText objects or return string as-is"""
        if isinstance(field, dict):
            return field.get('text', str(field))
        return str(field) if field else ""

    def _safe_join_list(self, field) -> str:
        """Helper to safely join list fields"""
        if isinstance(field, list):
            return ', '.join(str(item) for item in field if item)
        return str(field) if field else ""

    def convert_structured_resume_to_text(self, structured_resume: dict) -> str:
        """Convert structured resume data back to text format for ATS analysis with minimal data loss"""
        try:
            resume_text = []

            # Header information
            if structured_resume.get("name"):
                resume_text.append(f"Name: {structured_resume['name']}")
            if structured_resume.get("title"):
                resume_text.append(f"Title: {structured_resume['title']}")

            # Contact information - comprehensive extraction
            contact_info = structured_resume.get("contact_info", {})
            if contact_info:
                resume_text.append("\nContact Information:")
                # Standard fields
                for field in ['email', 'phone', 'location', 'linkedin', 'website', 'github', 'portfolio']:
                    if contact_info.get(field):
                        resume_text.append(f"{field.title()}: {contact_info[field]}")

                # Handle any additional contact fields
                for key, value in contact_info.items():
                    if key not in ['email', 'phone', 'location', 'linkedin', 'website', 'github', 'portfolio'] and value:
                        resume_text.append(f"{key.title()}: {value}")

            # Professional summary
            if structured_resume.get("professional_summary"):
                resume_text.append(f"\nProfessional Summary:\n{structured_resume['professional_summary']}")

            # Skills - handle both list and string formats
            skills = structured_resume.get("skills")
            if skills:
                if isinstance(skills, list):
                    resume_text.append(f"\nSkills:\n{', '.join(str(skill) for skill in skills if skill)}")
                else:
                    resume_text.append(f"\nSkills:\n{skills}")

            # Work experience - comprehensive extraction
            work_experience = structured_resume.get("work_experience", [])
            if work_experience:
                resume_text.append("\nWork Experience:")
                for exp in work_experience:
                    # Handle company as LinkText or string
                    company_name = self._extract_text_from_linktext(exp.get('company', ''))
                    job_title = exp.get('job_title', '')

                    resume_text.append(f"\n{job_title} at {company_name}")

                    # Add all available fields
                    if exp.get("dates"):
                        resume_text.append(f"Duration: {exp['dates']}")
                    if exp.get("location"):
                        resume_text.append(f"Location: {exp['location']}")

                    # Handle responsibilities/achievements
                    responsibilities = exp.get("responsibilities", [])
                    if responsibilities:
                        resume_text.append("Responsibilities:")
                        for resp in responsibilities:
                            resume_text.append(f"• {resp}")

                    # Handle any additional fields that might exist
                    for key, value in exp.items():
                        if key not in ['job_title', 'company', 'dates', 'location', 'responsibilities'] and value:
                            if isinstance(value, list):
                                resume_text.append(f"{key.title()}: {self._safe_join_list(value)}")
                            else:
                                resume_text.append(f"{key.title()}: {value}")

            # Education - comprehensive extraction
            education = structured_resume.get("education", [])
            if education:
                resume_text.append("\nEducation:")
                for edu in education:
                    # Handle institution as LinkText or string
                    institution_name = self._extract_text_from_linktext(edu.get('institution', ''))
                    degree = edu.get('degree', '')

                    resume_text.append(f"\n{degree} from {institution_name}")

                    if edu.get("graduation_year"):
                        resume_text.append(f"Graduation Year: {edu['graduation_year']}")

                    # Handle any additional education fields
                    for key, value in edu.items():
                        if key not in ['degree', 'institution', 'graduation_year'] and value:
                            resume_text.append(f"{key.title()}: {value}")

            # Projects - comprehensive extraction
            projects = structured_resume.get("projects", [])
            if projects:
                resume_text.append("\nProjects:")
                for project in projects:
                    # Handle project name as LinkText or string
                    project_name = self._extract_text_from_linktext(project.get('name', ''))
                    resume_text.append(f"\n{project_name}")

                    if project.get("description"):
                        resume_text.append(f"Description: {project['description']}")
                    if project.get("technologies"):
                        resume_text.append(f"Technologies: {self._safe_join_list(project['technologies'])}")
                    if project.get("dates"):
                        resume_text.append(f"Duration: {project['dates']}")

                    # Handle any additional project fields
                    for key, value in project.items():
                        if key not in ['name', 'description', 'technologies', 'dates'] and value:
                            if isinstance(value, list):
                                resume_text.append(f"{key.title()}: {self._safe_join_list(value)}")
                            else:
                                resume_text.append(f"{key.title()}: {value}")

            # Certifications - comprehensive extraction
            certifications = structured_resume.get("certifications", [])
            if certifications:
                resume_text.append("\nCertifications:")
                for cert in certifications:
                    cert_name = cert.get('name', '')
                    resume_text.append(f"• {cert_name}")

                    # Handle institution as LinkText or string
                    if cert.get("institution"):
                        institution = self._extract_text_from_linktext(cert.get('institution', ''))
                        resume_text.append(f"  Issued by: {institution}")
                    elif cert.get("issuer"):  # Alternative field name
                        resume_text.append(f"  Issued by: {cert['issuer']}")

                    if cert.get("year"):
                        resume_text.append(f"  Year: {cert['year']}")

                    # Handle any additional certification fields
                    for key, value in cert.items():
                        if key not in ['name', 'institution', 'issuer', 'year'] and value:
                            resume_text.append(f"  {key.title()}: {value}")

            # Achievements - comprehensive extraction
            achievements = structured_resume.get("acheivements", [])  # Note: keeping the typo for consistency
            if achievements:
                resume_text.append("\nAchievements:")
                for achievement in achievements:
                    title = achievement.get('title', '')
                    resume_text.append(f"\n{title}")

                    if achievement.get("tasks"):
                        for task in achievement["tasks"]:
                            resume_text.append(f"• {task}")
                    if achievement.get("date"):
                        resume_text.append(f"Date: {achievement['date']}")

                    # Handle any additional achievement fields
                    for key, value in achievement.items():
                        if key not in ['title', 'tasks', 'date'] and value:
                            if isinstance(value, list):
                                resume_text.append(f"{key.title()}: {self._safe_join_list(value)}")
                            else:
                                resume_text.append(f"{key.title()}: {value}")

            # Handle any additional top-level fields that might exist
            processed_keys = {
                'name', 'title', 'contact_info', 'professional_summary', 'skills',
                'work_experience', 'education', 'projects', 'certifications',
                'acheivements', 'activity_title'
            }

            for key, value in structured_resume.items():
                if key not in processed_keys and value:
                    resume_text.append(f"\n{key.replace('_', ' ').title()}:")
                    if isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                # Handle complex objects
                                for sub_key, sub_value in item.items():
                                    if sub_value:
                                        resume_text.append(f"• {sub_key.replace('_', ' ').title()}: {sub_value}")
                            else:
                                resume_text.append(f"• {item}")
                    elif isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            if sub_value:
                                resume_text.append(f"{sub_key.replace('_', ' ').title()}: {sub_value}")
                    else:
                        resume_text.append(str(value))

            result = "\n".join(resume_text)
            logging.info(f"Converted structured resume to text. Original keys: {list(structured_resume.keys())}, Text length: {len(result)}")
            return result

        except Exception as e:
            logging.error(f"Error converting structured resume to text: {str(e)}")
            logging.error(f"Structured resume keys: {list(structured_resume.keys()) if isinstance(structured_resume, dict) else 'Not a dict'}")
            return "Error converting resume data"

    async def create_resume(self, resume, resume_id: str, user_id,content_hash:str):
        try:
          with self.db_session as session:

            new_resume = ResumeModel(
                id=resume_id,
                candidate_name=resume.get("candidate_name", ""),
                phone_number=resume.get("phone_number", ""),
                email=resume.get("email", ""),
                skills=resume.get("skills", []),
                experience=resume.get("experience", []),
                projects=resume.get("projects", []),
                content=resume.get("content", ""),
                content_hash=content_hash,
                created_by=user_id
            )
            session.add(new_resume)
            session.commit()
            session.refresh(new_resume)
            return new_resume
        except Exception as e:
            raise e

    async def delete_resume_from_db(self, resume_id: str,user_id) -> None:
        """Delete resume from database"""
        try:
            with self.db_session as session:
                resume = session.query(ResumeModel).filter_by(id=resume_id, created_by=user_id).first()
                if not resume:
                    raise HTTPException(status_code=404, detail="Resume not found")
                
                session.delete(resume)
                session.commit()
        except Exception as e:
            raise e