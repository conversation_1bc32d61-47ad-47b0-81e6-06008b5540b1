import json
import logging
import random

from datetime import datetime
from typing import List
from bs4 import BeautifulSoup
from pydantic import BaseModel
from openai import OpenAI, LengthFinishReasonError

from playwright.async_api import async_playwright

from datetime import datetime
from typing import List

client = OpenAI()
# Define a Pydantic model for the job details response
class JobDetails(BaseModel):
    job_title: str
    company: str
    location: str
    summary: str
    apply_url: str

# Define the system prompt for job extraction
SYSTEM_PROMPT_JOB_EXTRACTION = """
You are an AI assistant specialized in extracting and summarizing job posting details.
Given the cleaned text extracted from a job posting page, extract the following details:
1. Job Title
2. Company Name
3. Location
4. A concise Job Summary (covering key responsibilities and requirements)
5. Application URL (if available; otherwise, return "Not found")

Format your answer as a JSON object with the keys "job_title", "company", "location", "summary", and "apply_url".
"""


from playwright.async_api import async_playwright
import random

async def fetch_page_content(url: str) -> str:
    """
    Uses Playwright to asynchronously launch a headless Chromium browser, navigate to the URL,
    and return the rendered HTML content while avoiding detection using stealth tactics and cookies.
    """
    async with async_playwright() as p:
        # Launch Chromium with advanced stealth options
        browser = await p.chromium.launch(
            headless=True,
            args=[
                "--disable-blink-features=AutomationControlled",  # Prevent bot detection
                "--disable-infobars",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                f"--window-size={random.randint(1200, 1920)},{random.randint(800, 1080)}"  # Randomized browser size
            ]
        )

        context = await browser.new_context(
            user_agent=f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{random.randint(500, 600)}.36 "
                       f"(KHTML, like Gecko) Chrome/{random.randint(90, 110)}.0.{random.randint(3000, 5000)}.0 "
                       f"Safari/{random.randint(500, 600)}.36",
            viewport={"width": random.randint(1200, 1920), "height": random.randint(800, 1080)},  # Set viewport size
            java_script_enabled=True,
        )

        page = await context.new_page()

        cookies = [
            {"name": "ajs_anonymous_id", "value": "5260d799-b360-43bb-89fe-9331ed3c684d", "domain": ".wellfound.com", "path": "/"},
            {"name": "_wellfound", "value": "e2f37e653ba4e9cd6f036ea7658f27e8.o", "domain": ".wellfound.com", "path": "/"},
            {"name": "_clck", "value": "18vn6rx%7C2%7Cftr%7C0%7C1883", "domain": "wellfound.com", "path": "/"},
            {"name": "_hjSessionUser_1444722", "value": "eyJpZCI6ImRlOWQ0Y2IwLWM0Y2MtNTAxOS05ZjNmLTE1M2MyY2Q1YmE3MSIsImNyZWF0ZWQiOjE3NDA1NTQ1MzM1MzUsImV4aXN0aW5nIjp0cnVlfQ==", "domain": "wellfound.com", "path": "/"},
            {"name": "_ga", "value": "GA1.1.*********.1740554534", "domain": "wellfound.com", "path": "/"},
            {"name": "_clsk", "value": "1w4yto0%7C1740563732266%7C2%7C1%7Ck.clarity.ms%2Fcollect", "domain": ".wellfound.com", "path": "/"},
            {"name": "_ga_705F94181H", "value": "GS1.1.1740563633.3.1.1740563792.33.0.0", "domain": ".wellfound.com", "path": "/"},
            {"name": "datadome", "value": "VpHmsR1D~mv2xWGoeATTkZv9I8jiytGUaBVpVaVYpl09sZ9xNCj_OTo7FlNj_O6aJroyoxY1lHGhxfX_v3vX6K_HqcD7pPH4tT1T5OC12rmErP4QDUSFWTEIX3BL5New", "domain": ".wellfound.com", "path": "/"},
            {"name": "cf_clearance", "value": "S5Ys86fpOknzSyTwx8NfEv9obC7148kirJtUmf0gIQ4-1740639216-1.2.1.1-omdRT_NhL_x9F.Jzr.mC03xDBM_7ADrEnw6tWr2YONhIUBQ6c.pgFPHOkxMwcOKo6YF_FrUcFvbpjC.H6rIwbVhLZ7IdyoBps1h0yppPRvKXk8lOCwS8e1aAwqoAvgaSQzJ4VwN9SYELIXQGaXRFk.uyhRbzG4O82XFCadOgH6nnXQNh1mR5MHE1.pXdkxQX0eAm24UoMxnEzGooNh.3_mpJlEkhLAq2TaGtQ4PvfcZ2mN4LkvvANdV2Ya7yG6HNu5kAfyGgPHhYJatUTRWKyX51ttZUqaYteMEuwpW0QO4", "domain": ".wellfound.com", "path": "/"}
        ]
        await context.add_cookies(cookies)

        try:
            # Set custom headers to mimic real browsing behavior
            await page.set_extra_http_headers({
                "Accept-Language": "en-US,en;q=0.9",
                "Referer": "https://www.google.com/",
                "Upgrade-Insecure-Requests": "1",
            })

            # Navigate to the page
            await page.goto(url, wait_until="networkidle", timeout=60000)

            # Introduce a random delay to mimic human-like behavior
            await page.wait_for_timeout(random.randint(1000, 3000))

            # Extract and return the page content
            content = await page.content()
            return content

        except Exception as e:
            print(f"Failed to fetch page {url}: {e}")
            return ""

        finally:
            await browser.close()


async def extract_jobs_from_page_with_playwright(url: str) -> dict:
    """
    Combines the asynchronous fetching of page content via Playwright with the HTML cleaning
    and job extraction process.
    """
    html_content = await fetch_page_content(url)
    logging.info(html_content)
    return extract_jobs_from_page(html_content)


def extract_jobs_from_page(html_content: str) -> dict:
    """
    Cleans the provided HTML content and uses OpenAI's API (with the updated beta.chat.completions.parse method)
    to extract job details.

    Returns:
        A dictionary containing job details structured according to the JobDetails model.
    """
    # Preprocess HTML to remove non-visible tags
    soup = BeautifulSoup(html_content, "html.parser")
    for tag in soup(["script", "style", "noscript", "header", "footer", "meta", "link"]):
        tag.decompose()
    
    # Extract visible text and remove extra whitespace
    visible_text = soup.get_text(separator="\n")
    clean_text = "\n".join(line.strip() for line in visible_text.splitlines() if line.strip())

    logging.info(clean_text)
    # Build the prompt content: include the cleaned text and today's date to ensure recent context
    prompt_content = (
        f"<cleaned_text>{clean_text}</cleaned_text>\n"
        f"<today-date>{datetime.today().strftime('%B %d, %Y')}</today-date>"
    )

    try:
        # Use the updated OpenAI API call (client.beta.chat.completions.parse)
        response = client.beta.chat.completions.parse(
            model="gpt-4o-mini-2024-07-18",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT_JOB_EXTRACTION},
                {"role": "user", "content": prompt_content}
            ],
            response_format=JobDetails
        )

        job_message = response.choices[0].message
        if job_message.parsed:
            parsed_response = job_message.parsed.dict()
            logging.info(f"Extracted job details: {json.dumps(parsed_response)}")
            return parsed_response
        elif job_message.refusal:
            logging.warning(f"Job extraction refused: {job_message.refusal}")
            return None
    except Exception as e:
        if isinstance(e, LengthFinishReasonError):
            logging.error(f"Content exceeds context window: {str(e)}")
            raise Exception("The content is too large to process.")
        logging.error(f"Failed to extract job details: {str(e)}")
        return None
    


# Define a Pydantic model for structured job summaries
class JobSummary(BaseModel):
    id: int
    job_title: str
    company: str
    location: str
    summary: str
    apply_url: str

class FinalJobsList(BaseModel):
    jobs_list: List[JobSummary]

# System prompt to extract structured job details from search results
SYSTEM_PROMPT_JOB_SUMMARY = """
You are an AI assistant that extracts and summarizes job postings from search results.

### **Instructions:**
1. **Extract key details** from each job snippet:
   - Job Title
   - Company Name
   - Location
   - A concise Job Summary (responsibilities, skills, requirements)
   - Application URL
2. **Remove irrelevant metadata** (timestamps, recruiter info, etc.).
3. **Return a structured JSON list**.

### **Example Input:**
[
    {
        "title": "Data Analyst at Milo Mobility • Bangalore Urban | Wellfound",
        "url": "https://wellfound.com/jobs/3210960-data-analyst",
        "snippet": "Milo Mobility is hiring a Data Analyst in Bangalore Urban - Apply now on Wellfound! Discover Find Jobs For ... Posted: 2 weeks ago • Recruiter recently active. Save. Apply Now. ... Microsoft Power BI. Hiring contact. Pavan Venkatesh. Business Head. About the job. As our Data Analyst, you will be responsible for the entire data lifecycle, from ..."
    },
    {
        "title": "Data Analyst at Binocs.co • Bangalore Urban | Wellfound",
        "url": "https://wellfound.com/jobs/3166746-data-analyst",
        "snippet": "Its a 6 months internship from Bangalore Location. Key Responsibilities: Assist with analyzing data to identify trends and insights. Help create reports and dashboards to track business metrics (e.g., customer acquisition, retention, product usage). Collaborate with teams like Product, Marketing, and Sales to understand data needs."
    }
]

### **Expected Output:**
```json
[
    {
        "job_title": "Data Analyst",
        "company": "Milo Mobility",
        "location": "Bangalore Urban",
        "summary": "Milo Mobility is looking for a Data Analyst to manage the entire data lifecycle, ensure data accuracy, and support business intelligence using Power BI.",
        "apply_url": "https://wellfound.com/jobs/3210960-data-analyst"
    },
    {
        "job_title": "Data Analyst Intern",
        "company": "Binocs.co",
        "location": "Bangalore Urban",
        "summary": "6-month internship focusing on data trends, report creation, and cross-team collaboration with Product, Marketing, and Sales.",
        "apply_url": "https://wellfound.com/jobs/3166746-data-analyst"
    }
]
"""

def summarize_jobs(job_results):
    """
    Takes job search results and summarizes key details using OpenAI.
    
    Args:
        job_results (List[dict]): List of job search results (title, url, snippet).

    Returns:
        List[dict]: Summarized job details in a structured format.
    """
    try:
        response = client.beta.chat.completions.parse(
            model="gpt-4o-mini-2024-07-18",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT_JOB_SUMMARY},
                {"role": "user", "content": json.dumps(job_results)}
            ],
            response_format=FinalJobsList
        )

        job_message = response.choices[0].message
        if job_message.parsed:
            parsed_response = job_message.parsed.dict()
            logging.info(f"Summarized job listings: {json.dumps(parsed_response)}")
            return parsed_response["jobs_list"]
        elif job_message.refusal:
            logging.warning(f"Job summarization refused: {job_message.refusal}")
            return None
    except Exception as e:
        logging.error(f"Failed to summarize job listings: {e}")
        return None

